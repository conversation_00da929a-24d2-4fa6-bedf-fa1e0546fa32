#!/usr/bin/env python3

import os
import json
import re
import zipfile
import requests
from datetime import datetime
from PyPDF2 import PdfReader

DEFAULT_API_URL = "https://bonum.in/boatsai/insert_invoice_data.php"  # API endpoint for invoice data
DEFAULT_API_KEY = "your-api-key"  # Replace with actual API keypo
DEFAULT_API_TIMEOUT = 30  # Timeout in seconds


def extract_zip_files(folder_path):
    """Extract all zip files in the specified folder"""
    extracted_files = []

    print("\n" + "-"*80)
    print("📋 EXTRACT_ZIP_FILES FUNCTION STARTED")
    print("-"*80)
    temp_folder = os.path.join(folder_path, "extracted")
    if not os.path.exists(temp_folder):
        os.makedirs(temp_folder)
        print(f"📁 Created extraction folder: {temp_folder}")
    else:
        print(f"📁 Extraction folder already exists: {temp_folder}")
    zip_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.zip')]
    print(f"🔍 Found {len(zip_files)} zip file(s) in {folder_path}")

    for file in zip_files:
        zip_path = os.path.join(folder_path, file)
        zip_name = os.path.splitext(file)[0]
        extract_subfolder = os.path.join(temp_folder, zip_name)

        print(f"\n📦 Processing zip file: {file}")
        print(f"   - Zip path: {zip_path}")
        print(f"   - Extract subfolder: {extract_subfolder}")

        if os.path.exists(extract_subfolder) and os.listdir(extract_subfolder):
            print(f"   ⚠️ Zip file {file} already extracted to {extract_subfolder}")
            pdf_files_in_subfolder = [f for f in os.listdir(extract_subfolder) if f.lower().endswith('.pdf')]
            print(f"   📄 Found {len(pdf_files_in_subfolder)} previously extracted PDF file(s)")

            for extracted_file in pdf_files_in_subfolder:
                extracted_path = os.path.join(extract_subfolder, extracted_file)
                extracted_files.append(extracted_path)
                print(f"     ✅ Found previously extracted {extracted_file}")

            continue
        print(f"   🔄 Extracting {file}...")

        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                if not os.path.exists(extract_subfolder):
                    os.makedirs(extract_subfolder)
                zip_ref.extractall(extract_subfolder)

                pdf_files_in_subfolder = [f for f in os.listdir(extract_subfolder) if f.lower().endswith('.pdf')]
                print(f"   📄 Extracted {len(pdf_files_in_subfolder)} PDF file(s)")

                for extracted_file in pdf_files_in_subfolder:
                    extracted_path = os.path.join(extract_subfolder, extracted_file)
                    extracted_files.append(extracted_path)
                    print(f"     ✅ Extracted {extracted_file}")

            print(f"   ✅ Successfully extracted {file}")
        except Exception as e:
            print(f"   ❌ Error extracting {file}: {str(e)}")

    return extracted_files

def send_data_to_api(json_file_path, api_url=None):
    """Send the generated JSON data to the specified API endpoint"""
    if not api_url:
        api_url = DEFAULT_API_URL

    if not api_url:
        print("⚠️ No API URL provided, skipping API upload")
        return False

    print(f"\n🌐 Sending data to API: {api_url}")

    try:
        # Read the JSON file
        with open(json_file_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)

        print(f"📄 Loaded JSON data from: {json_file_path}")
        print(f"📊 Data contains {json_data.get('total_pdfs_processed', 0)} processed PDFs")
        invoices_array = []
        if "invoice" in json_data:
            for pdf_key, pages_data in json_data["invoice"].items():
                for page_data in pages_data:
                    if "IMEI/Serial Numbers" in page_data:
                        imei_data = page_data["IMEI/Serial Numbers"]
                        if isinstance(imei_data, list):
                            if imei_data:  # If list has items
                                page_data["IMEI_Serial_Numbers"] = ", ".join(str(imei) for imei in imei_data)
                                page_data["IMEI"] = ", ".join(str(imei) for imei in imei_data)  # Alternative field name
                            else:  # If list is empty
                                page_data["IMEI_Serial_Numbers"] = ""
                                page_data["IMEI"] = ""
                        else:
                            # If it's already a string
                            page_data["IMEI_Serial_Numbers"] = str(imei_data) if imei_data else ""
                            page_data["IMEI"] = str(imei_data) if imei_data else ""

                        print(f"   🔧 IMEI field formatted: '{page_data.get('IMEI_Serial_Numbers', '')}' for API")
                    else:
                        page_data["IMEI_Serial_Numbers"] = ""
                        page_data["IMEI"] = ""
                    invoices_array.append(page_data)

        # Create the API-compatible data structure (matching the expected format)
        api_data = {
            "timestamp": json_data.get("timestamp", ""),
            "processing_date": json_data.get("processing_date", ""),
            "total_pdfs_processed": json_data.get("total_pdfs_processed", 0),
            "total_zip_files_extracted": json_data.get("total_zip_files_extracted", 0),
            "total_pdfs_from_zip": json_data.get("total_pdfs_from_zip", 0),
            "invoices_folder": json_data.get("invoices_folder", ""),
            "invoice": json_data.get("invoice", {}),  # Keep original invoice structure
            "invoices": invoices_array  # Also include flattened invoices array
        }
        print(f"🔄 Transformed data: {len(invoices_array)} invoice records prepared for API")

        imei_count = sum(1 for invoice in invoices_array if invoice.get("IMEI_Serial_Numbers", "").strip())
        print(f"📱 IMEI data: {imei_count} invoices contain IMEI/Serial numbers")
        # Prepare the request
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'PDF-Extractor/1.0'
        }
        # Send the data to API
        print("🚀 Sending request to API...")
        response = requests.post(
            api_url,
            json=api_data,
            headers=headers,
            timeout=DEFAULT_API_TIMEOUT
        )
        # Check response
        if response.status_code == 200:
            print("✅ Data successfully sent to API!")
            print(f"📡 Response: {response.text[:200]}..." if len(response.text) > 200 else f"📡 Response: {response.text}")
            return True
        else:
            print(f"❌ API request failed with status code: {response.status_code}")
            print(f"📡 Response: {response.text[:200]}..." if len(response.text) > 200 else f"📡 Response: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print(f"⏰ API request timed out after {DEFAULT_API_TIMEOUT} seconds")
        return False
    except requests.exceptions.ConnectionError:
        print("🔌 Failed to connect to API - check your internet connection")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ API request failed: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Error sending data to API: {str(e)}")
        return False

def extract_perfect_data(folder_path=None, api_url=None, api_key=None):
    """Extract data perfectly matching the actual PDF structure from specified folder"""
    print("="*80)
    print("🎯 PERFECT PDF EXTRACTOR - FINAL VERSION")
    print("="*80)
    print("Extracting data to perfectly match actual PDF content...")
    if folder_path is None:
        invoice_folder = "Invoice"
    else:
        invoice_folder = folder_path
    if not os.path.exists(invoice_folder):
        print(f"❌ Error: Folder '{invoice_folder}' does not exist!")
        return None

    print(f"📁 Reading PDFs from folder: {invoice_folder}")

    results = {
        "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
        "processing_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "total_pdfs_processed": 0,
        "total_zip_files_extracted": 0,
        "total_pdfs_from_zip": 0,
        "invoices_folder": invoice_folder,
        "invoice": {}
    }
    print(f"🔍 Checking for zip files in folder: {invoice_folder}")
    extracted_pdf_files = extract_zip_files(invoice_folder)
    pdf_files = []
    try:
        for file in os.listdir(invoice_folder):
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(invoice_folder, file))
        pdf_files.extend(extracted_pdf_files)

    except Exception as e:
        print(f"❌ Error reading folder '{invoice_folder}': {str(e)}")
        return None

    if not pdf_files:
        print(f"❌ No PDF files found in folder '{invoice_folder}' (including extracted zip files)")
        return None

    print(f"📄 Found {len(pdf_files)} PDF file(s) to process (including {len(extracted_pdf_files)} from zip files)")
    results["total_pdfs_from_zip"] = len(extracted_pdf_files)
    temp_folder = os.path.join(invoice_folder, "extracted")
    if os.path.exists(temp_folder):
        zip_subfolders = [d for d in os.listdir(temp_folder) if os.path.isdir(os.path.join(temp_folder, d))]
        results["total_zip_files_extracted"] = len(zip_subfolders)

    for pdf_path in pdf_files:
        print(f"\n🔍 Processing: {os.path.basename(pdf_path)}")

        try:
            pdf_data = extract_pdf_perfectly(pdf_path)
            if pdf_data:
                pdf_key = f"__{os.path.basename(pdf_path).replace('.pdf', '_pdf')}"
                results["invoice"][pdf_key] = pdf_data
                results["total_pdfs_processed"] += 1
                for page in pdf_data:
                    gross = page.get('Gross Amount', 'N/A')
                    discount = page.get('Discount', 'N/A')
                    taxable = page.get('Taxable Value', 'N/A')
                    igst = page.get('IGST', 'N/A')
                    sgst = page.get('SGST', 'N/A')
                    total = page.get('Grand Total', 'N/A')
                    irn = page.get('IRN', 'N/A')
                    print(f"✅ Page {page['Page']}: Gross={gross}, Discount={discount}, Taxable={taxable}, IGST={igst}, SGST={sgst}, Total={total}, IRN={irn}")
        except Exception as e:
            print(f"❌ Error processing {os.path.basename(pdf_path)}: {str(e)}")

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"perfect_extracted_data_{timestamp}.json"

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=4, ensure_ascii=False)

    print(f"\n🎉 Perfect extraction complete!")
    print(f"📄 Output: {output_file}")
    print(f"📊 Total PDFs processed: {results['total_pdfs_processed']}")
    print(f"📦 Total ZIP files extracted: {results['total_zip_files_extracted']}")
    print(f"📄 Total PDFs from ZIP files: {results['total_pdfs_from_zip']}")

    #Send data to API
    api_success = send_data_to_api(output_file, api_url)
    if api_success:
        print("🌐 ✅ Data successfully sent to API!")
    else:
        print("🌐 ⚠️ Failed to send data to API (data still saved locally)")

    return output_file

def clean_product_title(title):
    if not title:
        return ""

    # First check if it's an address - if so, return empty string
    if is_address_line(title):
        return ""

    title = title.strip()

    # Remove common address prefixes that might appear in extracted text
    address_prefixes = [
        r'^(billing|shipping|delivery)\s+(address|to):\s*',
        r'^(ship\s+to|bill\s+to|sold\s+by):\s*',
        r'^(customer\s+)?address:\s*',
        r'^(registered|corporate|head|branch)\s+office:\s*'
    ]

    for prefix_pattern in address_prefixes:
        title = re.sub(prefix_pattern, '', title, flags=re.IGNORECASE)

    # Standard cleaning
    title = re.sub(r'\s*\d+\s*(?:Rs\.?|₹)?\s*\d*\.?\d*$', '', title, flags=re.IGNORECASE)
    title = re.sub(r'\s*\(Pack of \d+\)$', '', title, flags=re.IGNORECASE)
    title = re.sub(r'\s*\b\d{4,}\b$', '', title)  # Remove trailing large numbers (likely codes)
    title = re.sub(r'\s*(HSN|SAC|SKU|FSN|Model|Code):?\s*[A-Za-z0-9-]+$', '', title, flags=re.IGNORECASE)
    title = re.sub(r'\s+', ' ', title).strip()
    title = re.sub(r'[^a-zA-Z\s]$', '', title)

    # Final check after cleaning - if it became an address after cleaning, return empty
    if is_address_line(title):
        return ""

    # Additional check: if title is too short after cleaning, likely not a valid product title
    if len(title.strip()) < 5:
        return ""

    return title

def is_likely_address_context(text, line_index, all_lines):
    """
    Check if the text appears in an address context by examining surrounding lines
    """
    if not text or not all_lines:
        return False

    # Check lines above and below for address context
    context_range = 3  # Check 3 lines above and below
    start_idx = max(0, line_index - context_range)
    end_idx = min(len(all_lines), line_index + context_range + 1)

    context_lines = all_lines[start_idx:end_idx]
    context_text = ' '.join(context_lines).lower()

    # Strong address context indicators
    address_context_indicators = [
        'billing address', 'shipping address', 'delivery address',
        'ship to', 'bill to', 'sold by', 'customer address',
        'registered office', 'corporate office', 'head office',
        'branch office', 'regional office'
    ]

    for indicator in address_context_indicators:
        if indicator in context_text:
            return True

    # Check if multiple lines in context contain address-like elements
    address_line_count = 0
    for line in context_lines:
        if is_address_line(line):
            address_line_count += 1

    # If more than half the context lines are address-like, this is likely an address context
    if address_line_count > len(context_lines) / 2:
        return True

    return False

def is_address_line(text):
    """Check if a line of text appears to be an address"""
    if not text:
        return False

    text_lower = text.lower().strip()

    # Enhanced address indicators - more comprehensive list
    address_indicators = [
        'road', 'street', 'st', 'avenue', 'ave', 'lane', 'ln', 'drive', 'dr',
        'boulevard', 'blvd', 'circle', 'cir', 'court', 'ct', 'place', 'pl',
        'square', 'sq', 'terrace', 'ter', 'way', 'park', 'nagar', 'colony',
        'sector', 'block', 'plot', 'flat', 'apartment', 'apt', 'floor', 'building',
        'complex', 'society', 'layout', 'extension', 'cross', 'main', 'phase',
        'city', 'town', 'village', 'district', 'state', 'country', 'pin', 'zip',
        'pincode', 'postal', 'code', 'area', 'locality', 'region', 'zone',
        'near', 'opp', 'opposite', 'behind', 'beside', 'next', 'adjacent',
        # Additional Indian address terms
        'marg', 'gali', 'mohalla', 'chowk', 'bazar', 'market', 'vihar', 'enclave',
        'kunj', 'puram', 'gram', 'gaon', 'tehsil', 'taluka', 'mandal', 'dist',
        'distt', 'district', 'ward', 'wing', 'tower', 'heights', 'residency',
        'gardens', 'villa', 'villas', 'homes', 'apartments', 'flats', 'plaza',
        'mall', 'center', 'centre', 'junction', 'crossing', 'bypass', 'highway',
        'expressway', 'metro', 'station', 'depot', 'terminal', 'airport',
        # Address context words
        'address', 'addr', 'location', 'situated', 'located', 'premises',
        'residence', 'office', 'shop', 'store', 'warehouse', 'factory',
        # Directional indicators
        'north', 'south', 'east', 'west', 'northeast', 'northwest', 'southeast', 'southwest',
        'n', 's', 'e', 'w', 'ne', 'nw', 'se', 'sw'
    ]

    # Enhanced address patterns - more comprehensive
    address_patterns = [
        # Standard address patterns
        r'\b\d+[-/,\s]+\w+\s+(road|street|st|avenue|ave|lane|ln|drive|dr|nagar|colony|marg|gali)\b',
        r'\b(flat|apartment|apt|floor|building|block|plot|house|shop|office)\s*[#\-\s]*\d+\b',
        r'\b\d{6}\b',  # PIN codes
        r'\b\d{5}[-\s]?\d{4}\b',  # ZIP codes
        r'\bpin\s*[:\-]?\s*\d{6}\b',
        r'\bzip\s*[:\-]?\s*\d{5}\b',
        r'\b(near|opp|opposite|behind|beside|next\s+to|adjacent\s+to)\s+\w+',
        r'\b(phase|sector|block|plot|ward|wing|tower)\s*[:\-]?\s*\d+\b',
        r'\b\d+\s*(st|nd|rd|th)\s+(floor|cross|main|phase)\b',
        r'\b(door\s*no|house\s*no|h\.?\s*no|plot\s*no|flat\s*no|apt\s*no)\s*[:\-]?\s*\d+\b',

        # Indian specific patterns
        r'\b\d+[-/,\s]+(nagar|colony|vihar|enclave|puram|gram|gaon|mohalla|chowk)\b',
        r'\b(ward|sector|phase|block|plot|flat|apartment|building|tower|wing)\s*[:\-]?\s*[a-z]?\d+\b',
        r'\b\d+\s*(main|cross|ring)\s+(road|street|marg)\b',
        r'\b(near|opp|opposite)\s+(metro|station|hospital|school|temple|mosque|church|mall|market)\b',

        # Address format patterns
        r'\b\d+[a-z]?\s*[-/,]\s*\d+[a-z]?\b',  # House numbers like "123-A", "45/2"
        r'\b[a-z]\s*[-/]\s*\d+\b',  # Patterns like "A-123", "B/45"
        r'\b\d+\s*[a-z]\s*[-/,]\s*\d+\b',  # Patterns like "12A-34", "5B/67"

        # Multi-line address indicators
        r'(billing|shipping|delivery)\s+(address|to)',
        r'(ship\s+to|bill\s+to|sold\s+by|customer\s+address)',

        # State and city patterns (common Indian states/cities)
        r'\b(delhi|mumbai|bangalore|chennai|kolkata|hyderabad|pune|ahmedabad|jaipur|lucknow)\b',
        r'\b(maharashtra|karnataka|tamil\s+nadu|west\s+bengal|rajasthan|gujarat|uttar\s+pradesh|bihar|odisha|kerala)\b',

        # Postal patterns
        r'\b(post|postal)\s+(office|code)\b',
        r'\bpo\s*[:\-]?\s*\w+\b',

        # Geographic indicators
        r'\b(taluka|tehsil|mandal|dist|distt|district)\s*[:\-]?\s*\w+\b',

        # Building/location specific
        r'\b(ground|first|second|third|basement|mezzanine)\s+floor\b',
        r'\b(shop|office|flat|apartment|unit)\s+no\s*[:\-]?\s*\d+\b'
    ]

    # Check for address indicators
    for indicator in address_indicators:
        if indicator in text_lower:
            return True

    # Check for address patterns
    for pattern in address_patterns:
        if re.search(pattern, text_lower):
            return True

    # Enhanced multiple numbers check (common in addresses)
    numbers = re.findall(r'\b\d+\b', text)
    if len(numbers) >= 2:
        # If it has multiple numbers and some address-like words, likely an address
        address_like_words = ['no', 'floor', 'block', 'plot', 'flat', 'apt', 'building', 'house', 'shop', 'office', 'wing', 'tower', 'sector', 'phase', 'ward']
        if any(word in text_lower for word in address_like_words):
            return True

    # Check for address-like structure patterns
    # Pattern: Number + Word + Number (common in Indian addresses)
    if re.search(r'\b\d+\s+\w+\s+\d+\b', text_lower):
        # Additional check for address context
        if any(word in text_lower for word in ['main', 'cross', 'road', 'street', 'lane', 'sector', 'phase', 'block']):
            return True

    # Check for comma-separated address components
    if ',' in text and len(text.split(',')) >= 2:
        # If it has commas and address-like words, likely an address
        if any(word in text_lower for word in address_indicators[:20]):  # Check first 20 most common indicators
            return True

    return False

def is_valid_product_title(title):
    if not title:
        return False
    if len(title) < 3:
        return False
    if not re.search(r'[a-zA-Z]', title):
        return False
    if re.fullmatch(r'\d+|[A-Za-z0-9-]+|[A-Z]|[a-z]', title):
        return False

    # Primary address check - this is the most important filter
    if is_address_line(title):
        return False

    # Enhanced invalid patterns - more comprehensive address detection
    invalid_patterns = [
        r'^(Rs\.?|₹)?\s*\d+\.?\d*$',
        r'^\d+\s*(pcs|units|qty|piece|pieces)?$',
        r'^(HSN|SAC|SKU|FSN|IMEI|PAN|GSTIN|CIN|IRN)[:\s]*[A-Za-z0-9]*$',
        r'^(Contact|Phone|Email|Address|Website).*$',
        r'^(Total|Grand|Sub|Net|Final)\s*(Amount|Price|Cost|Value).*$',
        r'^(Tax|GST|IGST|SGST|CGST|VAT).*$',
        r'^(Qty|Quantity|Gross|Discount|Taxable).*$',
        r'^(Invoice|Bill|Receipt|Order).*$',
        r'^(Date|Time|Number|No\.?).*$',
        r'^[A-Z]{2,}\d+[A-Z]*$',
        r'^\d{2}-\d{2}-\d{4}$',
        r'^[A-Z0-9]{10,}$',

        # Enhanced address patterns
        r'^(Billing|Shipping|Delivery)\s+(Address|To).*$',
        r'^(Ship\s+To|Bill\s+To|Sold\s+By|Customer\s+Address).*$',
        r'^\d+[-/,\s]+\w+\s+(Road|Street|Avenue|Lane|Drive|Nagar|Colony|Marg|Gali).*$',
        r'^(Flat|Apartment|Floor|Building|Block|Plot|House|Shop|Office)\s*[#\-\s]*\d+.*$',
        r'^.*\b\d{6}\b.*$',  # Lines containing PIN codes
        r'^.*\bPIN\s*[:\-]?\s*\d{6}\b.*$',
        r'^(Ward|Sector|Phase|Block|Plot|Wing|Tower)\s*[:\-]?\s*[A-Za-z]?\d+.*$',
        r'^.*\b(Near|Opp|Opposite)\s+(Metro|Station|Hospital|School|Temple|Mall|Market).*$',
        r'^\d+\s*(Main|Cross|Ring)\s+(Road|Street|Marg).*$',
        r'^.*\b(Taluka|Tehsil|Mandal|Dist|District)\s*[:\-]?\s*\w+.*$',
        r'^.*\b(Ground|First|Second|Third|Basement)\s+Floor.*$',
        r'^.*\b(Post|Postal)\s+(Office|Code).*$',

        # Address format patterns
        r'^\d+[A-Za-z]?\s*[-/,]\s*\d+[A-Za-z]?.*$',  # House numbers like "123-A", "45/2"
        r'^[A-Za-z]\s*[-/]\s*\d+.*$',  # Patterns like "A-123", "B/45"
        r'^\d+\s*[A-Za-z]\s*[-/,]\s*\d+.*$',  # Patterns like "12A-34", "5B/67"

        # Common Indian city/state patterns
        r'^.*(Delhi|Mumbai|Bangalore|Chennai|Kolkata|Hyderabad|Pune|Ahmedabad|Jaipur|Lucknow).*$',
        r'^.*(Maharashtra|Karnataka|Tamil\s+Nadu|West\s+Bengal|Rajasthan|Gujarat|Uttar\s+Pradesh|Bihar|Odisha|Kerala).*$'
    ]

    for pattern in invalid_patterns:
        if re.match(pattern, title, re.IGNORECASE):
            return False

    # Enhanced non-product words including more address-related terms
    non_product_words = {
        'contact', 'flipkart', 'amazon', 'seller', 'buyer', 'customer', 'company',
        'address', 'phone', 'email', 'website', 'total', 'amount', 'price', 'cost',
        'tax', 'gst', 'igst', 'sgst', 'cgst', 'hsn', 'sac', 'qty', 'quantity',
        'gross', 'discount', 'taxable', 'invoice', 'bill', 'receipt', 'order',
        'date', 'time', 'number', 'signature', 'authorized', 'signatory', 'imei', 'serial',

        # Enhanced address-related words
        'road', 'street', 'avenue', 'lane', 'drive', 'boulevard', 'circle', 'court',
        'place', 'square', 'terrace', 'way', 'park', 'nagar', 'colony', 'sector',
        'block', 'plot', 'flat', 'apartment', 'floor', 'building', 'complex',
        'society', 'layout', 'extension', 'cross', 'main', 'phase', 'city', 'town',
        'village', 'district', 'state', 'country', 'pin', 'zip', 'pincode', 'postal',
        'area', 'locality', 'region', 'zone', 'near', 'opposite', 'behind', 'beside',
        'billing', 'shipping', 'delivery', 'door', 'house',

        # Additional Indian address terms
        'marg', 'gali', 'mohalla', 'chowk', 'bazar', 'market', 'vihar', 'enclave',
        'kunj', 'puram', 'gram', 'gaon', 'tehsil', 'taluka', 'mandal', 'dist',
        'distt', 'ward', 'wing', 'tower', 'heights', 'residency', 'gardens',
        'villa', 'villas', 'homes', 'apartments', 'flats', 'plaza', 'mall',
        'center', 'centre', 'junction', 'crossing', 'bypass', 'highway',
        'expressway', 'metro', 'station', 'depot', 'terminal', 'airport',
        'premises', 'residence', 'office', 'shop', 'store', 'warehouse', 'factory',
        'north', 'south', 'east', 'west', 'northeast', 'northwest', 'southeast', 'southwest'
    }

    words = title.lower().split()
    if len(words) > 0:
        non_product_count = sum(1 for word in words if word in non_product_words)
        # Stricter threshold for address detection - if more than 40% are address words, reject
        if non_product_count / len(words) > 0.4:
            return False

    # Additional check: if title contains multiple address indicators, reject
    address_word_count = sum(1 for word in words if word in ['road', 'street', 'nagar', 'colony', 'sector', 'block', 'plot', 'flat', 'apartment', 'building', 'floor', 'phase', 'main', 'cross'])
    if address_word_count >= 2:
        return False

    if all(len(word) < 2 for word in words):
        return False

    table_headers = [
        'title', 'qty', 'gross', 'discount', 'taxable', 'igst', 'sgst', 'total',
        'amount', 'price', 'rate', 'value', 'description', 'item', 'product'
    ]

    if title.lower().strip() in table_headers:
        return False
    if len(title) > 100:
        return False

    return True

def extract_product_title_vertical(text):
    print("Looking for Product Title using vertical scanning...")

    if not text:
        return ""
    lines = text.split('\n')
    product_headers = [
        'description', 'product', 'title', 'item', 'name', 'goods',
        'product title', 'product name', 'item description', 'description of goods',
        'product description', 'item name', 'commodity', 'article', 'details',
        'specification', 'model', 'brand', 'category', 'particulars'
    ]
    for i, line in enumerate(lines):
        line_lower = line.lower().strip()
        for header in product_headers:
            if (header in line_lower and
                len(line_lower) < 100 and  # Likely a header, not content
                not any(skip in line_lower for skip in ['contact', 'flipkart', 'phone', 'email', 'website', 'address'])):

                print(f"Found product header '{header}' in line {i}: {line.strip()}")
                for j in range(i + 1, min(i + 15, len(lines))):  # Check next 14 lines
                    if j >= len(lines):
                        break

                    candidate_line = lines[j].strip()
                    if not candidate_line:
                        continue
                    skip_words = [
                        'hsn', 'sac', 'qty', 'quantity', 'gross', 'discount', 'taxable',
                        'igst', 'sgst', 'cgst', 'total', 'amount', 'price', 'rate',
                        'fsn', 'sku', 'code', 'gst', 'tax', '₹', 'rs.', 'inr',
                        'contact', 'flipkart', 'phone', 'email', 'website', 'address',
                        'invoice', 'bill', 'receipt', 'order', 'date', 'time',
                        'signature', 'authorized', 'signatory', 'regd', 'office'
                    ]

                    if any(skip_word in candidate_line.lower() for skip_word in skip_words):
                        continue
                    if re.match(r'^\s*[\d\s\.\-\|\+\=\:\;\,]+\s*$', candidate_line):
                        continue
                    if re.match(r'^\s*(title|qty|gross|discount|taxable|igst|sgst|total|amount|price|rate)\s*$', candidate_line, re.IGNORECASE):
                        continue
                    # Enhanced address detection with context awareness
                    if is_address_line(candidate_line):
                        continue
                    # Check if this line appears in an address context
                    if is_likely_address_context(candidate_line, j, lines):
                        continue
                    if re.search(r'\d{6}|\d{10}|@|\.com', candidate_line, re.IGNORECASE):
                        continue
                    if (re.search(r'[A-Za-z]', candidate_line) and
                        len(candidate_line) > 10 and
                        not re.search(r'^\d+\s*%', candidate_line)):
                        full_product_desc = candidate_line
                        for k in range(j + 1, min(j + 5, len(lines))):
                            if k >= len(lines):
                                break
                            next_line = lines[k].strip()
                            if (re.search(r'\d+\s+[\d.]+\s+[-\d.]+', next_line) or
                                re.search(r'^\d+\s*$', next_line) or  # Just a number
                                any(word in next_line.lower() for word in ['qty', 'gross', 'discount', 'taxable', 'igst', 'total'])):
                                break
                            if (next_line and
                                re.search(r'[A-Za-z]', next_line) and
                                len(next_line) > 5 and
                                not re.search(r'^\d+\s*%', next_line) and
                                not any(skip_word in next_line.lower() for skip_word in skip_words) and
                                not is_address_line(next_line)):
                                full_product_desc += " " + next_line
                            else:
                                break
                        cleaned_candidate = clean_product_title(full_product_desc)
                        if is_valid_product_title(cleaned_candidate) and len(cleaned_candidate) > 5:
                            print(f"Found product title vertically: {cleaned_candidate}")
                            return cleaned_candidate

    print("Trying enhanced table structure scanning...")

    for i, line in enumerate(lines):
        line_stripped = line.strip()
        if not line_stripped:
            continue

        if re.search(r'^[A-Za-z][A-Za-z0-9\s\-\(\)\.]{10,}?\s+\d+\s+[\d.]+', line_stripped):
            product_match = re.match(r'^([A-Za-z][A-Za-z0-9\s\-\(\)\.]{10,}?)\s+\d+\s+[\d.]+', line_stripped)
            if product_match:
                product_candidate = product_match.group(1).strip()
                cleaned_candidate = clean_product_title(product_candidate)
                if is_valid_product_title(cleaned_candidate) and len(cleaned_candidate) > 5:
                    print(f"Found product title in table row: {cleaned_candidate}")
                    return cleaned_candidate

        if (re.search(r'[A-Za-z]', line_stripped) and
            len(line_stripped) > 15 and
            not re.match(r'^\s*[\d\s\.\-\|\+\=\:\;\,]+\s*$', line_stripped) and
            not re.search(r'^\d+\s*%', line_stripped)):

            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if re.search(r'\d+\s+[\d.]+\s+[-\d.]+', next_line):
                    cleaned_candidate = clean_product_title(line_stripped)
                    if is_valid_product_title(cleaned_candidate) and len(cleaned_candidate) > 5:
                        print(f"Found product title above financial data: {cleaned_candidate}")
                        return cleaned_candidate

    print("No product title found using vertical scanning")
    return ""

def extract_product_title_from_table(text):
    print("Looking for Product Title above table data...")
    lines = text.split("\n")
    financial_patterns = [
        r"^\s*\|?\s*\d+\s*\|?\s*[\d.,]+\s*\|?\s*[-\d.,]+\s*\|?\s*[\d.,]+",
        r"^\s*\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+",
        r"^\s*\|\s*\d+\s*\|\s*[\d.]+\s*\|\s*[-\d.]+\s*\|\s*[\d.]+",
        r"^\s*\d+\s+[\d.,]+\s+[\d.,]+\s+[\d.,]+",
    ]
    for i in range(2, len(lines)):
        line = lines[i].strip()
        is_financial_line = False
        for pattern in financial_patterns:
            if re.match(pattern, line):
                is_financial_line = True
                break
        if is_financial_line:
            print(f"Found financial data line {i}: {line}")
            for j in range(i-1, max(0, i-10), -1):  # Check up to 9 lines above
                prev_line = lines[j].strip()

                # Skip empty lines
                if not prev_line:
                    continue

                # Skip lines with known metadata (expanded list)
                skip_keywords = [
                    "igst", "sac", "hsn", "fsn", "cess", "qty", "quantity",
                    "gross", "discount", "taxable", "total", "amount", "gst", "tax",
                    "contact", "flipkart", "phone", "email", "website", "imei", "serial",
                    "invoice", "bill", "receipt", "order", "date", "time", "address",
                    "signature", "authorized", "signatory", "regd", "office", "cin", "pan"
                ]
                if any(kw in prev_line.lower() for kw in skip_keywords):
                    continue
                if re.match(r'^[\s\|\-\+\=\:\;\,]+$', prev_line) or re.match(r'^\s*[\d\s\.\-\|\:\;\,]+\s*$', prev_line):
                    continue
                if re.match(r'^\s*(title|qty|gross|discount|taxable|igst|sgst|total|amount|price|rate|description|product|item)\s*$', prev_line, re.IGNORECASE):
                    continue
                # Enhanced address detection with context awareness
                if is_address_line(prev_line):
                    continue
                # Check if this line appears in an address context
                if is_likely_address_context(prev_line, j, lines):
                    continue
                if re.search(r'\d{6}|\d{10}|@|\.com', prev_line, re.IGNORECASE):
                    continue
                if not re.search(r'[A-Za-z]', prev_line) or len(prev_line) < 3:
                    continue
                cleaned_title = clean_product_title(prev_line)
                if is_valid_product_title(cleaned_title) and len(cleaned_title) > 3:
                    print(f"Found product title above table: {cleaned_title}")
                    return cleaned_title
    print("Scanning for product names in table rows...")
    for i, line in enumerate(lines):
        line_stripped = line.strip()
        product_with_data_pattern = r'^([A-Za-z][A-Za-z0-9\s\-\(\)\.\/]{15,}?)\s+\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+'
        match = re.match(product_with_data_pattern, line_stripped)
        if match:
            product_candidate = match.group(1).strip()
            cleaned_title = clean_product_title(product_candidate)
            if is_valid_product_title(cleaned_title) and len(cleaned_title) > 10:
                print(f"Found product title in table row: {cleaned_title}")
                return cleaned_title

    print("Trying structured table format scanning...")

    for i in range(len(lines) - 1):
        line = lines[i].strip()
        if not line or len(line) < 10:  # Increased minimum length
            continue
        if (re.search(r'[A-Za-z]', line) and
            not re.match(r'^\s*[\d\s\.\-\|\+\=\:\;\,]+\s*$', line) and
            10 <= len(line) <= 150):  # Increased range for longer product names
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if re.search(r'\d+\s+[\d.]+\s+[-\d.]+', next_line):
                    # Additional address context check before accepting as product title
                    if is_likely_address_context(line, i, lines):
                        continue
                    cleaned_title = clean_product_title(line)
                    if is_valid_product_title(cleaned_title) and len(cleaned_title) > 10:
                        print(f"Found product title in structured table: {cleaned_title}")
                        return cleaned_title

    print("Scanning for product lines near FSN/HSN/IMEI blocks...")
    for i in range(len(lines) - 1):
        if any(keyword in lines[i].lower() for keyword in ["fsn", "imei", "hsn"]):
            # Look upwards for the actual title
            for j in range(i - 1, max(0, i - 5), -1):
                candidate = lines[j].strip()
                if not candidate or len(candidate) < 8:
                    continue
                if any(skip in candidate.lower() for skip in ["invoice", "igst", "discount", "amount", "value"]):
                    continue
                cleaned = clean_product_title(candidate)
                if is_valid_product_title(cleaned):
                    print(f"✅ Product title found near FSN/HSN block: {cleaned}")
                    return cleaned

    print("No product title found above table data")
    return ""

def extract_product_title(text):
    print("Looking for Product Title in text...")
    if not text:
        return ""
    product_title = extract_product_title_vertical(text)
    if product_title:
        return product_title
    product_title = extract_product_title_from_table(text)
    if product_title:
        return product_title
    print("Trying horizontal pattern matching...")
    text = re.sub(r'\s+', ' ', text).strip()
    patterns = [
        r'(?:Description|Item|Product|Name)\s*[:\-]?\s*([A-Za-z\s\-\(\)]{3,}(?=\s*(?:HSN|SAC|SKU|FSN|Qty|Quantity|Rs\.?|₹|\d+|$)))',
        r'Description of Goods\s*[:\-]?\s*([A-Za-z\s\-\(\)]{3,}(?=\s*(?:HSN|SAC|SKU|FSN|Qty|Quantity|Rs\.?|₹|\d+|$)))',
        r'(\b[A-Za-z\s\-\(\)]{3,})\s+(?:HSN|SAC)\s*:\s*[A-Za-z0-9-]+',
        r'(\b[A-Za-z\s\-\(\)]{3,})\s+(?:Qty|Quantity)\s*:\s*\d+',
        r'(\b[A-Za-z\s\-\(\)]{3,})\s+(?:Rs\.?|₹)\s*\d+\.?\d*',
        r'(\b[A-Za-z\s\-\(\)]{3,})\s+(?:SKU|FSN|Model)\s*:\s*[A-Za-z0-9-]+',
        r'\b([A-Z][A-Za-z\s\-\(\)]{3,}(?=\s*(?:HSN|SAC|SKU|FSN|Qty|Quantity|Rs\.?|₹|\d+|$)))',
        r'\b([A-Za-z\s\-\(\)]{3,})\s*(?=\d+\s*(?:pcs|units|qty|Rs\.?|₹|$))',
    ]

    for pattern in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            title = match.group(1).strip()
            cleaned_title = clean_product_title(title)
            if is_valid_product_title(cleaned_title):
                print(f"Found product title with horizontal pattern: {cleaned_title}")
                return cleaned_title
    print("Trying generic fallback patterns...")
    fallback_pattern = r'\b[A-Z][A-Za-z\s\-\(\)]{3,}\b'
    matches = re.findall(fallback_pattern, text)
    for match in matches:
        cleaned_title = clean_product_title(match)
        if is_valid_product_title(cleaned_title):
            print(f"Found product title with fallback pattern: {cleaned_title}")
            return cleaned_title
    print("No product title found")
    return ""

def extract_pdf_perfectly(pdf_path):
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            pages_data = []

            for page_num, page in enumerate(pdf_reader.pages):
                text = page.extract_text()
                if not text:
                    continue

                print(f"   📄 Extracting page {page_num + 1}...")
                data = {
                    "Page": page_num + 1,
                    "Customer Name": "",
                    "Shipping Address": "",
                    "Billing Address": "",
                    "Order ID": "",
                    "Order Date": "",
                    "Invoice Date": "",
                    "Invoice Number": "",
                    "PAN": "",
                    "CIN": "",
                    "GSTIN": "",
                    "IRN": "",
                    "Sold By": "",
                    "Product Title": "",
                    "Quantity": "",
                    "Gross Amount": "",
                    "Discount": "",
                    "Taxable Value": "",
                    "IGST": "",
                    "SGST": "",
                    "Grand Total": "",
                    "HSN/SAC": "",
                    "IMEI/Serial Numbers": [],
                    "Source PDF": os.path.basename(pdf_path).replace('.pdf', ''),
                    "Source Folder": os.path.dirname(pdf_path),
                    "Full Path": pdf_path
                }

                extract_perfect_fields(text, data)
                pages_data.append(data)

            return pages_data

    except Exception as e:
        print(f"   ❌ Error reading PDF: {str(e)}")
        return None

def extract_perfect_fields(text, data):
    """Extract fields perfectly based on actual PDF structure"""

    order_match = re.search(r'Order\s+(?:Id|ID)[:\s]*\n?\s*(OD\d+)', text, re.IGNORECASE)
    if order_match:
        data["Order ID"] = order_match.group(1)
        print(f"   ✅ Order ID: {data['Order ID']}")

    invoice_patterns = [
        r'Invoice\s+(?:No|Number)[:\s]*\n?\s*([A-Z0-9]+?)(?:Tax|$)',  # Remove "Tax" suffix
        r'Invoice\s+Number\s*#\s*([A-Z0-9]+?)(?:Tax|$)',  # Remove "Tax" suffix
        r'#\s*([A-Z0-9]{10,}?)(?:Tax|$)',  # Remove "Tax" suffix
        r'Invoice\s+(?:No|Number)[:\s]*\n?\s*([A-Z0-9]+)',  # Fallback without Tax removal
        r'Invoice\s+Number\s*#\s*([A-Z0-9]+)',  # Fallback without Tax removal
        r'#\s*([A-Z0-9]{10,})'  # Fallback without Tax removal
    ]

    for pattern in invoice_patterns:
        invoice_match = re.search(pattern, text, re.IGNORECASE)
        if invoice_match:
            invoice_number = invoice_match.group(1).strip()
            # Additional cleanup to remove "Tax" if it's still there
            invoice_number = re.sub(r'Tax$', '', invoice_number, flags=re.IGNORECASE)
            data["Invoice Number"] = invoice_number
            print(f"   ✅ Invoice Number: {data['Invoice Number']}")
            break

    customer_patterns = [
        r"Customer Name[:\s]+([^\n]+)",
        r"Bill To[:\s]+([^\n]+)",
        r"Billing Address[:\s]+([^\n]+)",
        r"Ship To[:\s]+([^\n]+)"
    ]

    for pattern in customer_patterns:
        customer_match = re.search(pattern, text)
        if customer_match:
            data["Customer Name"] = customer_match.group(1).strip()
            break

    order_date = extract_order_date(text)
    if order_date:
        data["Order Date"] = order_date
        print(f"   ✅ Order Date: {data['Order Date']}")

    invoice_date = extract_invoice_date(text)
    if invoice_date:
        data["Invoice Date"] = invoice_date
        print(f"   ✅ Invoice Date: {data['Invoice Date']}")

    gstin_code = extract_gstin(text)
    if gstin_code:
        data["GSTIN"] = gstin_code
        print(f"   ✅ GSTIN: {data['GSTIN']}")

    pan_code = extract_pan(text)
    if pan_code:
        data["PAN"] = pan_code
        print(f"   ✅ PAN: {data['PAN']}")

    cin_code = extract_cin(text)
    if cin_code:
        data["CIN"] = cin_code
        print(f"   ✅ CIN: {data['CIN']}")

    irn_code = extract_irn(text)
    if irn_code:
        data["IRN"] = irn_code
        print(f"   ✅ IRN: {data['IRN']}")

    shipping_address = extract_shipping_address(text)
    if shipping_address:
        data["Shipping Address"] = shipping_address
        print(f"   ✅ Shipping Address: {data['Shipping Address']}")

    billing_address = extract_billing_address(text)
    if billing_address:
        data["Billing Address"] = billing_address
        print(f"   ✅ Billing Address: {data['Billing Address']}")

    billing_match = re.search(r'Billing Address\s*\n\s*([^\n,]+)', text, re.IGNORECASE)
    if billing_match:
        data["Customer Name"] = billing_match.group(1).strip()
        print(f"   ✅ Customer: {data['Customer Name']}")

    billing_address = extract_billing_address(text)
    if billing_address:
        data["Billing Address"] = billing_address
        print(f"   ✅ Billing Address: {data['Billing Address']}")

    sold_by_patterns = [
        r'Sold By[:\s]*\n?\s*([^\n,]+)',
        r'Sold By[:\s]*([^,\n]+)'
    ]

    for pattern in sold_by_patterns:
        sold_by_match = re.search(pattern, text, re.IGNORECASE)
        if sold_by_match:
            data["Sold By"] = sold_by_match.group(1).strip()
            print(f"   ✅ Sold By: {data['Sold By']}")
            break

    hsn_sac_code = extract_hsn_sac(text)
    if hsn_sac_code:
        data["HSN/SAC"] = hsn_sac_code
        print(f"   ✅ HSN/SAC: {data['HSN/SAC']}")

    print(f"   💰 Extracting financial data...")

    quantity = extract_quantity(text)
    if quantity:
        data["Quantity"] = quantity
        print(f"   ✅ Quantity: {data['Quantity']}")

    gross_amount = extract_gross_amount(text)
    if gross_amount:
        data["Gross Amount"] = gross_amount
        print(f"   ✅ Gross Amount: {data['Gross Amount']}")

    discount = extract_discount(text)
    if discount is not None:  # Could be 0.0
        data["Discount"] = discount
        print(f"   ✅ Discount: {data['Discount']}")

    taxable_value = extract_taxable_value(text)
    if taxable_value:
        data["Taxable Value"] = taxable_value
        print(f"   ✅ Taxable Value: {data['Taxable Value']}")

    igst = extract_igst(text)
    if igst is not None:  # Could be 0.0
        data["IGST"] = igst
        print(f"   ✅ IGST: {data['IGST']}")

    sgst = extract_sgst(text)
    if sgst is not None:  # Could be 0.0
        data["SGST"] = sgst
        print(f"   ✅ SGST: {data['SGST']}")

    grand_total = extract_grand_total(text)
    if grand_total:
        data["Grand Total"] = grand_total
        print(f"   ✅ Grand Total: {data['Grand Total']}")

    product_title = extract_product_title(text)
    if product_title:
        data["Product Title"] = product_title
        print(f"   ✅ Product: {product_title}")

    imei_numbers = extract_imei_numbers(text)
    if imei_numbers:
        data["IMEI/Serial Numbers"] = imei_numbers
        print(f"   ✅ IMEI/Serial Numbers: {data['IMEI/Serial Numbers']}")
    else:
        data["IMEI/Serial Numbers"] = []  # Ensure the field exists even if empty

    if not data.get("Grand Total"):
        total_price_match = re.search(r'TOTAL PRICE[:\s]*(\d+\.\d{2})', text, re.IGNORECASE)
        if total_price_match:
            data["Grand Total"] = float(total_price_match.group(1))
            print(f"   ✅ Grand Total (from TOTAL PRICE): {data['Grand Total']}")

def extract_product_title_from_block(text):
    """
    Extracts the product title by scanning lines above the numeric table rows.
    Skips HSN, IGST, SAC lines and grabs the line above those.
    """
    lines = text.split("\n")
    for i in range(2, len(lines)):
        # Match line with Qty, Gross, Discount, etc. — e.g., 1 499.00 -25.00 451.43 22.57 474.00
        if re.match(r"\s*\d+\s+[\d.,]+\s+[-\d.,]+\s+[\d.,]+\s+[\d.,]+\s+[\d.,]+", lines[i]):
            # Check previous lines
            for j in range(i-1, 0, -1):
                prev_line = lines[j].strip()
                # Skip known metadata lines
                if any(kw in prev_line.lower() for kw in ["igst", "sac", "hsn", "fsn", "cess"]):
                    continue
                if len(prev_line.strip()) > 3:
                    print(f"✅ Product title found: {prev_line}")
                    return prev_line
    print("⚠️ Product title not found using table block pattern.")
    return None

def extract_hsn_sac(text):
    """Extract HSN/SAC code from text using multiple patterns and strategies"""
    print("Looking for HSN/SAC codes in text...")

    hsn_sac_patterns = [
        # Standard pattern from the sample text
        r"HSN/SAC:\s*(\d+)",
        # Alternative patterns
        r"HSN/SAC[:\s]+(\d+)",
        r"HSN\s*Code[:\s]+(\d+)",
        r"SAC\s*Code[:\s]+(\d+)",
        r"HSN[:\s]+(\d+)",
        r"SAC[:\s]+(\d+)",
        # Patterns with colon
        r"HSN/SAC\s*:\s*(\d+)",
        r"HSN\s*:\s*(\d+)",
        r"SAC\s*:\s*(\d+)",
        # Patterns with dash
        r"HSN/SAC\s*-\s*(\d+)",
        r"HSN\s*-\s*(\d+)",
        r"SAC\s*-\s*(\d+)",
        # Patterns with HSN/SAC in table format
        r"HSN/SAC.*?\n.*?(\d+)",
        r"HSN.*?\n.*?(\d+)",
        r"SAC.*?\n.*?(\d+)",
        # Pattern for HSN/SAC followed by digits without space (common in invoices)
        r"HSN/SAC:(\d+)",
        r"HSN/SAC\s*:\s*(\d+)(?:[A-Z]|\s)",
        # Pattern for FSN followed by HSN/SAC
        r"FSN:.*?HSN/SAC:\s*(\d+)",
        # Pattern for HSN/SAC in product description
        r"HSN/SAC:\s*(\d+)[^\d\s]",
        # Patterns for HSN/SAC in product tables
        r"Product Title.*?HSN/SAC:?\s*(\d+)",
        r"Description.*?HSN/SAC:?\s*(\d+)",
        r"Item.*?HSN/SAC:?\s*(\d+)",
        # Pattern for HSN/SAC in a table cell
        r"HSN/SAC\s*\n\s*(\d+)",
        # Pattern for HSN/SAC code followed by percentage (common in invoices)
        r"HSN/SAC:?\s*(\d+).*?(\d+\.?\d*)\s*%",
        # Very generic pattern for any number after HSN/SAC
        r"HSN/SAC.*?(\d{4,10})",
        # Pattern for HSN code in brackets
        r"HSN\s*\(\s*(\d+)\s*\)",
        # Pattern for HSN code with slash
        r"HSN\s*/\s*(\d+)",
        # Pattern for HSN code with equals
        r"HSN\s*=\s*(\d+)",
        # Pattern for HSN code in a specific format
        r"HSN\s*:\s*(\d+)",
        # Pattern for HSN code in a table
        r"HSN\s*\|\s*(\d+)",
        # Pattern for HSN code in a list
        r"HSN\s*-\s*(\d+)",
        # Pattern for HSN code with no space
        r"HSN:(\d+)",
        # Pattern for HSN code with space
        r"HSN\s+(\d+)",
        # More generic pattern
        r"HSN.*?(\d{4,10})"
    ]

    for pattern in hsn_sac_patterns:
        hsn_sac_match = re.search(pattern, text, re.IGNORECASE)
        if hsn_sac_match:
            hsn_sac_code = hsn_sac_match.group(1).strip()
            print(f"Found HSN/SAC code: {hsn_sac_code} with pattern: {pattern}")
            return hsn_sac_code

    print("No HSN/SAC code found")
    return None

def extract_pan(text):
    """Extract PAN code from text using multiple patterns and strategies"""
    print("Looking for PAN codes in text...")
    pan_patterns = [
        r"PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN[:\s]+([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s*:\s*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s*-\s*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s*=\s*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN:([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN.*?\n.*?([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s*(?:No|Number)[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s*\(\s*([A-Z]{5}[0-9]{4}[A-Z]{1})\s*\)",
        r"PAN\s*/\s*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s*\|\s*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s+([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN[:\s]*([A-Z]{5}\.?[0-9]{4}\.?[A-Z]{1})",
        r"Company.*?PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"Seller.*?PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"Tax.*?PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"(?:Company|Seller|Tax|GST).*?PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"Address.*?PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s*\n\s*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"\b([A-Z]{5}[0-9]{4}[A-Z])\b"
    ]

    for pattern in pan_patterns:
        pan_match = re.search(pattern, text, re.IGNORECASE)
        if pan_match:
            pan_code = pan_match.group(1).strip().upper()
            if re.match(r'^[A-Z]{5}[0-9]{4}[A-Z]$', pan_code):
                print(f"Found PAN code: {pan_code} with pattern: {pattern}")
                return pan_code

    print("No PAN code found")
    return None

def extract_gstin(text):
    """Extract GSTIN code from text using multiple patterns and strategies"""
    print("Looking for GSTIN codes in text...")
    gstin_patterns = [
        r"GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN[:\s]+([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s*:\s*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s*-\s*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s*=\s*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN:([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN.*?\n.*?([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s*(?:No|Number)[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s*\(\s*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])\s*\)",
        r"GSTIN\s*/\s*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s*\|\s*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s+([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN[:\s]*([0-9]{2}\.?[A-Z0-9]{10}\.?[0-9]\.?[A-Z]\.?[A-Z0-9])",
        r"Company.*?GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"Seller.*?GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"Tax.*?GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"(?:Company|Seller|Tax|GST).*?GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"Address.*?GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s*\n\s*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN.*?([0-9A-Z]{15})",
        r"Billing.*?GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"Shipping.*?GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"\b([0-9]{2}[A-Z0-9]{13})\b"
    ]

    for pattern in gstin_patterns:
        gstin_match = re.search(pattern, text, re.IGNORECASE)
        if gstin_match:
            gstin_code = gstin_match.group(1).strip().upper()
            if re.match(r'^[0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9]$', gstin_code) and len(gstin_code) == 15:
                print(f"Found GSTIN code: {gstin_code} with pattern: {pattern}")
                return gstin_code

    print("No GSTIN code found")
    return None

def extract_invoice_date(text):
    """Extract Invoice Date from text using patterns based on actual PDF analysis"""
    print("Looking for Invoice Date in text...")

    invoice_date_patterns = [
        r"(\d{2}-\d{2}-\d{4})\s+Invoice Date:",
        r"(\d{1,2}-\d{1,2}-\d{4})\s+Invoice Date:",
        r"(\d{2}/\d{2}/\d{4})\s+Invoice Date:",
        r"(\d{1,2}/\d{1,2}/\d{4})\s+Invoice Date:",
        r"Invoice Date:\s*\n?\s*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date:\s*\n?\s*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date:\s*\n?\s*(\d{2}/\d{2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date:\s*\n?\s*(\d{1,2}/\d{1,2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date[:\s]+(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date[:\s]+(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date[:\s]+(\d{2}/\d{2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date[:\s]+(\d{1,2}/\d{1,2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date\s*\n\s*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date\s*\n\s*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date\s*\n\s*(\d{2}/\d{2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date\s*\n\s*(\d{1,2}/\d{1,2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Inv\.?\s*Date[:\s]*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Inv\.?\s*Date[:\s]*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Date of Invoice[:\s]*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Date of Invoice[:\s]*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Tax Invoice.*?Invoice Date[:\s]*\n?\s*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Tax Invoice.*?Invoice Date[:\s]*\n?\s*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice.*?Date[:\s]*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice.*?Date[:\s]*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"(\d{2}\.\d{2}\.\d{4})\s+Invoice Date:",
        r"Invoice Date[:\s]*(\d{2}\.\d{2}\.\d{4})",
        r"Invoice Date[:\s]*(\d{1,2}\.\d{1,2}\.\d{4})",
        r"Invoice Date.*?(\d{1,2}[-/\.]\d{1,2}[-/\.]\d{4})",
    ]

    for pattern in invoice_date_patterns:
        date_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if date_match:
            invoice_date = date_match.group(1).strip()
            # Clean up the date (remove time if present for consistency)
            if ',' in invoice_date:
                invoice_date = invoice_date.split(',')[0].strip()
            print(f"Found Invoice Date: {invoice_date} with pattern: {pattern}")
            return invoice_date

    print("No Invoice Date found")
    return None

def extract_order_date(text):
    """Extract Order Date from text using patterns based on actual PDF analysis"""
    print("Looking for Order Date in text...")
    order_date_patterns = [
        r"(\d{2}-\d{2}-\d{4})\s+Order Date:",
        r"(\d{1,2}-\d{1,2}-\d{4})\s+Order Date:",
        r"(\d{2}/\d{2}/\d{4})\s+Order Date:",
        r"(\d{1,2}/\d{1,2}/\d{4})\s+Order Date:",
        r"Order Date:\s*\n?\s*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date:\s*\n?\s*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date:\s*\n?\s*(\d{2}/\d{2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date:\s*\n?\s*(\d{1,2}/\d{1,2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date[:\s]+(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date[:\s]+(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date[:\s]+(\d{2}/\d{2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date[:\s]+(\d{1,2}/\d{1,2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date\s*\n\s*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date\s*\n\s*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date\s*\n\s*(\d{2}/\d{2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date\s*\n\s*(\d{1,2}/\d{1,2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order\.?\s*Date[:\s]*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order\.?\s*Date[:\s]*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Date of Order[:\s]*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Date of Order[:\s]*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Purchase Order.*?Order Date[:\s]*\n?\s*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Purchase Order.*?Order Date[:\s]*\n?\s*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order.*?Date[:\s]*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order.*?Date[:\s]*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"(\d{2}\.\d{2}\.\d{4})\s+Order Date:",
        r"Order Date[:\s]*(\d{2}\.\d{2}\.\d{4})",
        r"Order Date[:\s]*(\d{1,2}\.\d{1,2}\.\d{4})",
        r"Order Date\s*-\s*(\d{2}-\d{2}-\d{4})",
        r"Order Date\s*=\s*(\d{2}-\d{2}-\d{4})",
        r"Order Date\s*-\s*(\d{1,2}-\d{1,2}-\d{4})",
        r"Order Date\s*=\s*(\d{1,2}-\d{1,2}-\d{4})",
        r"Order Date\s*\(\s*(\d{2}-\d{2}-\d{4})\s*\)",
        r"Order Date\s*\(\s*(\d{1,2}-\d{1,2}-\d{4})\s*\)",
        r"Order Date\s*/\s*(\d{2}-\d{2}-\d{4})",
        r"Order Date\s*/\s*(\d{1,2}-\d{1,2}-\d{4})",
        r"Order Date\s*\|\s*(\d{2}-\d{2}-\d{4})",
        r"Order Date\s*\|\s*(\d{1,2}-\d{1,2}-\d{4})",
        r"Order Date.*?(\d{1,2}[-/\.]\d{1,2}[-/\.]\d{4})",
    ]

    for pattern in order_date_patterns:
        date_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if date_match:
            order_date = date_match.group(1).strip()
            if ',' in order_date:
                order_date = order_date.split(',')[0].strip()
            print(f"Found Order Date: {order_date} with pattern: {pattern}")
            return order_date

    print("No Order Date found")
    return None

def extract_quantity(text):
    print("Looking for Quantity in text...")

    quantity_patterns = [
        r'\|\s*(\d+)\s*\|\s*[\d.]+\s*\|\s*[-\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+',
        r'(\d+)\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 4 188.00 -60.00 114.28 13.72 0.00 128.00
        r'(\d+)\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 1 699.00 -0.00 592.38 53.31 53.31 699.00
        r'(\d+)\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 1 499.00 -25.00 451.43 22.57 474.00
        r'Qty[|\s]*(\d+)',
        r'Quantity[:\s]*(\d+)',
        r'Qty[:\s]*(\d+)',
        r'QTY[:\s]*(\d+)',
        r'TOTAL QTY[:\s]*(\d+)',
        r'pack of (\d+)',
        r'(\d+) piece',
        r'(\d+) pcs',
    ]

    for pattern in quantity_patterns:
        qty_match = re.search(pattern, text, re.IGNORECASE)
        if qty_match:
            quantity = int(qty_match.group(1))
            print(f"Found Quantity: {quantity} with pattern: {pattern}")
            return quantity

    print("No Quantity found, defaulting to 1")
    return 1  # Default to 1 if not found

def extract_gross_amount(text):
    """Extract Gross Amount from text using multiple patterns"""
    print("Looking for Gross Amount in text...")

    gross_patterns = [
        r'\|\s*\d+\s*\|\s*([\d.]+)\s*\|\s*[-\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+',
        r'\d+\s+([\d.]+)\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 4 188.00 -60.00 114.28 13.72 0.00 128.00
        r'\d+\s+([\d.]+)\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 1 699.00 -0.00 592.38 53.31 53.31 699.00
        r'\d+\s+([\d.]+)\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 1 499.00 -25.00 451.43 22.57 474.00
        r'Gross\s*Amount[|\s]*(\d+\.\d{2})',
        r'Gross[|\s]*(\d+\.\d{2})',
        r'Gross Amount[:\s]*(\d+\.\d{2})',
        r'Gross[:\s]*(\d+\.\d{2})',
        r'Amount[:\s]*(\d+\.\d{2})',
        r'Amount.*?(\d+\.\d{2})',
    ]

    for pattern in gross_patterns:
        gross_match = re.search(pattern, text, re.IGNORECASE)
        if gross_match:
            gross_amount = float(gross_match.group(1))
            print(f"Found Gross Amount: {gross_amount} with pattern: {pattern}")
            return gross_amount

    print("No Gross Amount found")
    return None

def extract_discount(text):
    """Extract Discount from text using multiple patterns"""
    print("Looking for Discount in text...")

    discount_patterns = [
        r'\|\s*\d+\s*\|\s*[\d.]+\s*\|\s*([-\d.]+)\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+',
        r'\d+\s+[\d.]+\s+([-\d.]+)\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 4 188.00 -60.00 114.28 13.72 0.00 128.00
        r'\d+\s+[\d.]+\s+([-\d.]+)\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 1 699.00 -0.00 592.38 53.31 53.31 699.00
        r'\d+\s+[\d.]+\s+([-\d.]+)\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 1 499.00 -25.00 451.43 22.57 474.00
        r'Discount[|\s]*(-?\d+\.\d{2})',
        r'Discount[:\s]*(-?\d+\.\d{2})',
        r'Disc[:\s]*(-?\d+\.\d{2})',
        r'(\d+)% off',
        r'Discount.*?(\d+\.\d{2})',
    ]

    for pattern in discount_patterns:
        discount_match = re.search(pattern, text, re.IGNORECASE)
        if discount_match:
            discount_val = float(discount_match.group(1))
            discount = abs(discount_val)  # Make positive
            print(f"Found Discount: {discount} with pattern: {pattern}")
            return discount

    print("No Discount found, defaulting to 0.0")
    return 0.0  # Default to 0 if not found

def extract_taxable_value(text):
    """Extract Taxable Value from text using multiple patterns"""
    print("Looking for Taxable Value in text...")

    taxable_patterns = [
        r'\|\s*\d+\s*\|\s*[\d.]+\s*\|\s*[-\d.]+\s*\|\s*([\d.]+)\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+',
        r'\d+\s+[\d.]+\s+[-\d.]+\s+([\d.]+)\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 4 188.00 -60.00 114.28 13.72 0.00 128.00
        r'\d+\s+[\d.]+\s+[-\d.]+\s+([\d.]+)\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 1 699.00 -0.00 592.38 53.31 53.31 699.00
        r'\d+\s+[\d.]+\s+[-\d.]+\s+([\d.]+)\s+[\d.]+\s+[\d.]+',  # 1 499.00 -25.00 451.43 22.57 474.00
        r'Taxable\s*Value[|\s]*(\d+\.\d{2})',
        r'Taxable[|\s]*(\d+\.\d{2})',
        r'Taxable Value[:\s]*(\d+\.\d{2})',
        r'Taxable[:\s]*(\d+\.\d{2})',
        r'Tax Base[:\s]*(\d+\.\d{2})',
        r'Taxable Amount[:\s]*(\d+\.\d{2})',
    ]

    for pattern in taxable_patterns:
        taxable_match = re.search(pattern, text, re.IGNORECASE)
        if taxable_match:
            taxable_value = float(taxable_match.group(1))
            print(f"Found Taxable Value: {taxable_value} with pattern: {pattern}")
            return taxable_value

    print("No Taxable Value found")
    return None

def extract_igst(text):
    """Extract IGST from text using multiple patterns"""
    print("Looking for IGST in text...")

    igst_patterns = [
        r'\|\s*\d+\s*\|\s*[\d.]+\s*\|\s*[-\d.]+\s*\|\s*[\d.]+\s*\|\s*([\d.]+)\s*\|\s*[\d.]+\s*\|\s*[\d.]+',
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+([\d.]+)\s+[\d.]+\s+[\d.]+',  # 4 188.00 -60.00 114.28 13.72 0.00 128.00
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+([\d.]+)\s+[\d.]+\s+[\d.]+',  # 1 699.00 -0.00 592.38 53.31 53.31 699.00 (IGST position)
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+([\d.]+)\s+[\d.]+',  # 1 499.00 -25.00 451.43 22.57 474.00 (IGST position)
        r'IGST[|\s]*(\d+\.\d{2})',
        r'IGST[:\s]*(\d+\.\d{2})',
        r'IGST.*?(\d+\.\d{2})',
        r'Integrated GST[:\s]*(\d+\.\d{2})',
        r'IGST:\s*(\d+\.\d{2})%',
        r'IGST\s*@\s*\d+%[:\s]*(\d+\.\d{2})',
    ]

    for pattern in igst_patterns:
        igst_match = re.search(pattern, text, re.IGNORECASE)
        if igst_match:
            igst_value = float(igst_match.group(1))
            print(f"Found IGST: {igst_value} with pattern: {pattern}")
            return igst_value

    print("No IGST found, defaulting to 0.0")
    return 0.0  # Default to 0 if not found

def extract_sgst(text):
    """Extract SGST from text using multiple patterns"""
    print("Looking for SGST in text...")

    sgst_patterns = [
        r'\|\s*\d+\s*\|\s*[\d.]+\s*\|\s*[-\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*([\d.]+)\s*\|\s*[\d.]+',
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+([\d.]+)\s+[\d.]+',  # 4 188.00 -60.00 114.28 13.72 0.00 128.00
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+([\d.]+)\s+[\d.]+',  # 1 699.00 -0.00 592.38 53.31 53.31 699.00 (SGST position)
        r'SGST[|\s]*(\d+\.\d{2})',
        r'SGST[:\s]*(\d+\.\d{2})',
        r'SGST.*?(\d+\.\d{2})',
        r'State GST[:\s]*(\d+\.\d{2})',
        r'SGST:\s*(\d+\.\d{2})%',
        r'SGST\s*@\s*\d+%[:\s]*(\d+\.\d{2})',
        r'CGST[:\s]*(\d+\.\d{2})',
        r'Central GST[:\s]*(\d+\.\d{2})',
    ]

    for pattern in sgst_patterns:
        sgst_match = re.search(pattern, text, re.IGNORECASE)
        if sgst_match:
            sgst_value = float(sgst_match.group(1))
            print(f"Found SGST: {sgst_value} with pattern: {pattern}")
            return sgst_value

    print("No SGST found, defaulting to 0.0")
    return 0.0  # Default to 0 if not found

def extract_grand_total(text):
    """Extract Grand Total from text using multiple patterns"""
    print("Looking for Grand Total in text...")

    total_patterns = [
        r'\|\s*\d+\s*\|\s*[\d.]+\s*\|\s*[-\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*([\d.]+)',
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+([\d.]+)',  # 4 188.00 -60.00 114.28 13.72 0.00 128.00
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+([\d.]+)',  # 1 699.00 -0.00 592.38 53.31 53.31 699.00 (Total position)
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+([\d.]+)',  # 1 499.00 -25.00 451.43 22.57 474.00 (Total position)
        r'TOTAL PRICE[:\s]*(\d+\.\d{2})',
        r'Total[|\s]*(\d+\.\d{2})',
        r'Grand Total[:\s]*(\d+\.\d{2})',
        r'Total Amount[:\s]*(\d+\.\d{2})',
        r'Final Total[:\s]*(\d+\.\d{2})',
        r'Net Total[:\s]*(\d+\.\d{2})',
        r'Total[:\s]*(\d+\.\d{2})',
        r'Invoice Total[:\s]*(\d+\.\d{2})',
        r'Bill Total[:\s]*(\d+\.\d{2})',
        r'Total.*?(\d+\.\d{2})',
    ]

    for pattern in total_patterns:
        total_match = re.search(pattern, text, re.IGNORECASE)
        if total_match:
            grand_total = float(total_match.group(1))
            print(f"Found Grand Total: {grand_total} with pattern: {pattern}")
            return grand_total

    print("No Grand Total found")
    return None

def extract_cin(text):
    """Extract CIN (Corporate Identification Number) from text using multiple patterns"""
    print("Looking for CIN in text...")
    cin_patterns = [
        r"CIN[:\s-]\s*(U\d{5}[A-Z]{2}\d{4}PTC\d{6})",
        r"CIN[:\s-]\s*(L\d{5}[A-Z]{2}\d{4}PLC\d{6})",
        r"CIN[:\s-]\s*([UL]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"CIN[:\s]+([A-Z0-9]{21})",
        r"CIN:\s*([A-Z0-9]+)",
        r"CIN\s*:\s*([A-Z0-9]+)",
        r"CIN\s+([A-Z0-9]+)",
        r"CIN[:\s]+([A-Z0-9]+)",
        r"CIN\s*-\s*([A-Z0-9]+)",
        r"CIN\s*Number\s*:\s*([A-Z0-9]+)",
        r"Corporate\s+Identification\s+Number\s*:\s*([A-Z0-9]+)",
        r"CIN:\s*(U74999KA2019PTC129964)",
        r"CIN\s*:\s*(U74999KA2019PTC129964)",
        r"CIN.*?([UL][0-9A-Z]{20})",
        r"CIN[:\s]*([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"CIN[:\s]+([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"CIN\s*:\s*([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"CIN\s*=\s*([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"CIN:([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"CIN.*?\n.*?([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"CIN\s*(?:No|Number)[:\s]*([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"CIN\s*\(\s*([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})\s*\)",
        r"CIN\s*/\s*([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"CIN\s*\|\s*([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"CIN\s+([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"Corporate Identification Number[:\s]*([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"Corporate ID[:\s]*([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"Company.*?CIN[:\s]*([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"Seller.*?CIN[:\s]*([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"Address.*?CIN[:\s]*([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"CIN\s*\n\s*([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"CIN[:\s]*([LU]\d{5}\.?[A-Z]{2}\.?\d{4}\.?[A-Z]{3}\.?\d{6})",
    ]

    for pattern in cin_patterns:
        cin_match = re.search(pattern, text, re.IGNORECASE)
        if cin_match:
            cin_code = cin_match.group(1).strip().upper()
            if re.match(r'^[LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6}$', cin_code) and len(cin_code) == 21:
                print(f"Found CIN: {cin_code} with pattern: {pattern}")
                return cin_code

    print("No CIN found")
    return None

def extract_irn(text):
    """Extract IRN (Invoice Reference Number) from text using comprehensive patterns"""
    print("Looking for IRN in text...")
    irn_patterns = [
        r"IRN\s*-\s*([a-f0-9]+)",
        r"IRN[:\s]+([a-f0-9]+)",
        r"IRN\s*:\s*([a-f0-9]+)",
        r"Invoice Reference Number[:\s]+([a-f0-9]+)",
        r"IRN[:\s]*([a-f0-9]+)",
        r"IRN\s*=\s*([a-f0-9]+)",
        r"IRN\s*#\s*([a-f0-9]+)",
        r"IRN:([a-f0-9]+)",
        r"IRN.*?\n.*?([a-f0-9]+)",
        r"IRN\s*\|\s*([a-f0-9]+)",
        r"IRN\s*(?:No|Number)[:\s]*([a-f0-9]+)",
        r"IRN\s*(?:No|Number)\s*:\s*([a-f0-9]+)",
        r"IRN\s*\(\s*([a-f0-9]+)\s*\)",
        r"IRN\s*\[\s*([a-f0-9]+)\s*\]",
        r"IRN\s*/\s*([a-f0-9]+)",
        r"IRN\s*-\s*([a-f0-9]+)",
        r"IRN\s+([a-f0-9]+)",
        r"Invoice Reference Number[:\s]*([a-f0-9]+)",
        r"Invoice Reference[:\s]*([a-f0-9]+)",
        r"Invoice Ref[:\s]*([a-f0-9]+)",
        r"Inv\s*Ref[:\s]*([a-f0-9]+)",
        r"E-Invoice.*?IRN[:\s]*([a-f0-9]+)",
        r"e-invoice.*?IRN[:\s]*([a-f0-9]+)",
        r"Electronic\s*Invoice.*?IRN[:\s]*([a-f0-9]+)",
        r"IRN\s*\n\s*([a-f0-9]+)",
        r"IRN\s*\n\s*:\s*([a-f0-9]+)",
        r"GST.*?IRN[:\s]*([a-f0-9]+)",
        r"Tax.*?IRN[:\s]*([a-f0-9]+)",
        r"IRN\s*[:\-=]\s*([a-f0-9]+)",
        r"IRN\s*[:\-=]\s*([a-f0-9-]+)",
        r"IRN[:\s]*([A-Fa-f0-9]+)",
        r"IRN\s*-\s*([A-Fa-f0-9]+)",
        r"IRN\s*:\s*([A-Fa-f0-9]+)",
        r"IRN\s*=\s*([A-Fa-f0-9]+)",
        r"IRN[:\s]*([a-f0-9-]{32,})",
        r"IRN[:\s]*([A-Fa-f0-9-]{32,})",
        r"([a-f0-9]{32,64})",
        r"([A-Fa-f0-9]{32,64})",
        r"Document.*?IRN[:\s]*([a-f0-9]+)",
        r"Invoice.*?IRN[:\s]*([a-f0-9]+)",
        r"IRN\s*Code[:\s]*([a-f0-9]+)",
        r"IRN\s*Value[:\s]*([a-f0-9]+)",
        r"IRN.*?([a-f0-9]{32,})",
        r"IRN.*?([A-Fa-f0-9]{32,})",
    ]

    for pattern in irn_patterns:
        irn_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if irn_match:
            irn_code = irn_match.group(1).strip().lower()
            irn_code = re.sub(r'[-\s]', '', irn_code)
            if re.match(r'^[a-f0-9]{32,64}$', irn_code) and len(irn_code) >= 32:
                print(f"Found IRN: {irn_code} with pattern: {pattern}")
                return irn_code

    print("No IRN found")
    return None

def clean_address_text(address_text):
    """Clean address text by removing Order ID, Invoice Number, Order Date, Invoice Date, and other non-address elements"""
    if not address_text:
        return ""

    # Remove Order ID patterns
    address_text = re.sub(r'Order\s+(?:Id|ID)[:\s]*[A-Z0-9-]+', '', address_text, flags=re.IGNORECASE)

    # Remove Invoice Number patterns
    address_text = re.sub(r'Invoice\s+(?:No|Number)[:\s]*[A-Z0-9-]+(?:Tax)?', '', address_text, flags=re.IGNORECASE)

    # Remove Order Date patterns
    address_text = re.sub(r'Order\s+Date[:\s]*\d{1,2}[-/]\d{1,2}[-/]\d{2,4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?', '', address_text, flags=re.IGNORECASE)

    # Remove Invoice Date patterns
    address_text = re.sub(r'Invoice\s+Date[:\s]*\d{1,2}[-/]\d{1,2}[-/]\d{2,4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?', '', address_text, flags=re.IGNORECASE)

    # Remove other common non-address elements that might get captured
    non_address_patterns = [
        r'GSTIN[:\s]*[A-Z0-9]{15}',
        r'PAN[:\s]*[A-Z]{5}[0-9]{4}[A-Z]',
        r'CIN[:\s]*[A-Z0-9]+',
        r'Phone[:\s]*[\d\s\-\+\(\)]+',
        r'Email[:\s]*[^\s]+@[^\s]+',
        r'Website[:\s]*[^\s]+',
        r'FSN[:\s]*[A-Z0-9]+',
        r'HSN[:\s]*\d+',
        r'SAC[:\s]*\d+',
        r'IRN[:\s]*[A-Fa-f0-9]{32,}',
        r'Total[:\s]*[\d.,]+',
        r'Amount[:\s]*[\d.,]+',
        r'₹\s*[\d.,]+',
        r'Rs\.?\s*[\d.,]+',
        r'INR\s*[\d.,]+',
        r'Qty[:\s]*\d+',
        r'Quantity[:\s]*\d+',
        r'Description[:\s]*',
        r'Product[:\s]*',
        r'Item[:\s]*'
    ]

    for pattern in non_address_patterns:
        address_text = re.sub(pattern, '', address_text, flags=re.IGNORECASE)

    # Clean up extra whitespace and punctuation
    address_text = re.sub(r'\s+', ' ', address_text)  # Normalize spaces
    address_text = re.sub(r'^[,\s\-:]+', '', address_text)  # Remove leading punctuation
    address_text = re.sub(r'[,\s\-:]+$', '', address_text)  # Remove trailing punctuation
    address_text = re.sub(r'[,\s]{2,}', ', ', address_text)  # Normalize comma spacing

    return address_text.strip()

def extract_billing_address(text):
    """Extract Billing Address from text using multiple patterns"""
    print("Looking for Billing Address in text...")

    billing_address_patterns = [
        # Enhanced patterns that specifically exclude Order ID, Invoice Number, dates
        r'Billing Address[:\s]*\n?\s*(.*?)(?=\n(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|Shipping Address|Sold By|GSTIN|PAN|Phone|Description|$))',
        r'Bill To[:\s]*\n?\s*(.*?)(?=\n(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|Ship To|Shipping Address|Sold By|GSTIN|PAN|Phone|Description|$))',
        r'Billing Address\s*\n\s*(.*?)(?=\n\n|Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|Shipping Address|Sold By|GSTIN|PAN|Phone|Description|$)',
        r'Bill Address[:\s]*\n?\s*(.*?)(?=\n(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|Ship Address|Shipping Address|Sold By|GSTIN|PAN|Phone|Description|$))',
        r'Bill\s+(.*?)(?=\n(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|Ship|Shipping Address|Sold By|GSTIN|PAN|Phone|Description|$))',
        r'Billing Address\s*\|\s*(.*?)(?=\n|$)',
        r'Billing Address\s+(.*?)(?=\n(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|Shipping Address|Sold By|GSTIN|PAN|Phone|Description|$))',
    ]

    for pattern in billing_address_patterns:
        billing_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if billing_match:
            billing_address = billing_match.group(1).strip()
            billing_address = re.sub(r'\n+', ', ', billing_address)  # Replace newlines with commas
            billing_address = re.sub(r'\s+', ' ', billing_address)   # Normalize spaces
            billing_address = re.sub(r',\s*$', '', billing_address)  # Remove trailing commas

            # Clean the address using the new cleaning function
            billing_address = clean_address_text(billing_address)

            for ending in ["Phone:", "GSTIN", "PAN", "Sold By", "Shipping Address", "Ship To", "Description"]:
                ending_match = re.search(f"(.*?)(?={ending})", billing_address, re.IGNORECASE)
                if ending_match:
                    billing_address = ending_match.group(1).strip()
                    break
            if len(billing_address) > 200:
                city_state_match = re.search(r"(.*?(?:Pune|Mumbai|Bengaluru|Karnataka|Maharashtra|Delhi|Chennai|Kolkata|Hyderabad|Ahmedabad|Surat)(?:\s*[-,]\s*\d{6})?)", billing_address, re.IGNORECASE)
                if city_state_match:
                    billing_address = city_state_match.group(1).strip()
            billing_address = re.sub(r'[,\s.]+$', '', billing_address)

            # Final validation - ensure it's still a meaningful address after cleaning
            if billing_address and len(billing_address) > 10:
                print(f"Found Billing Address: {billing_address} with pattern: {pattern}")
                return billing_address

    print("No Billing Address found")
    return None

def extract_shipping_address(text):
    """Extract Shipping Address from text using comprehensive patterns"""
    print("Looking for Shipping Address in text...")
    shipping_patterns = [
        # Enhanced patterns that specifically exclude Order ID, Invoice Number, dates
        r"Shipping Address[:\s]*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|Billing|GSTIN|PAN|CIN|$))",
        r"Ship To[:\s]*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|Bill|GSTIN|PAN|CIN|$))",
        r"Delivery Address[:\s]*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|Billing|GSTIN|PAN|CIN|$))",
        r"Shipping Address[:\s]*([^\n]+)",
        r"Ship To[:\s]*([^\n]+)",
        r"Delivery Address[:\s]*([^\n]+)",
        r"Shipping Address:\s*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|[A-Z]))",
        r"Ship To:\s*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|[A-Z]))",
        r"Shipping Address[:\s]*\n\s*([^\n]+)\s*\n\s*([^\n]+)\s*\n\s*([^\n]+)",
        r"Ship To[:\s]*\n\s*([^\n]+)\s*\n\s*([^\n]+)\s*\n\s*([^\n]+)",
        r"Shipping.*?\n.*?([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|Billing|$))",
        r"Ship.*?Address[:\s]*([^\n]+)",
        r"Delivery.*?Address[:\s]*([^\n]+)",
    ]

    for pattern in shipping_patterns:
        shipping_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if shipping_match:
            if shipping_match.lastindex and shipping_match.lastindex > 1:
                address_parts = [shipping_match.group(i) for i in range(1, shipping_match.lastindex + 1)]
                shipping_address = ", ".join([part.strip() for part in address_parts if part.strip()])
            else:
                shipping_address = shipping_match.group(1).strip()

            shipping_address = re.sub(r'\s+', ' ', shipping_address)  # Normalize spaces
            shipping_address = re.sub(r'\n+', ', ', shipping_address)  # Replace newlines with commas

            # Clean the address using the new cleaning function
            shipping_address = clean_address_text(shipping_address)

            # Final validation - ensure it's still a meaningful address after cleaning
            if shipping_address and len(shipping_address) > 10:
                print(f"Found Shipping Address: {shipping_address} with pattern: {pattern}")
                return shipping_address

    print("No Shipping Address found")
    return None

def extract_billing_address(text):
    print("Looking for Billing Address in text...")
    billing_patterns = [
        # Enhanced patterns that specifically exclude Order ID, Invoice Number, dates
        r"Billing Address[:\s]*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|Shipping|GSTIN|PAN|CIN|$))",
        r"Bill To[:\s]*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|Ship|GSTIN|PAN|CIN|$))",
        r"Billing[:\s]*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|Shipping|GSTIN|PAN|CIN|$))",
        r"Billing Address[:\s]*([^\n]+)",
        r"Bill To[:\s]*([^\n]+)",
        r"Billing[:\s]*([^\n]+)",
        r"Billing Address:\s*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|[A-Z]))",
        r"Bill To:\s*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|[A-Z]))",
        r"Billing Address[:\s]*\n\s*([^\n]+)\s*\n\s*([^\n]+)\s*\n\s*([^\n]+)",
        r"Bill To[:\s]*\n\s*([^\n]+)\s*\n\s*([^\n]+)\s*\n\s*([^\n]+)",
        r"Billing.*?\n.*?([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Order\s+(?:Id|ID|Date)|Invoice\s+(?:No|Number|Date)|Shipping|$))",
        r"Bill.*?Address[:\s]*([^\n]+)",
        r"Customer.*?Address[:\s]*([^\n]+)",
    ]

    for pattern in billing_patterns:
        billing_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if billing_match:
            if billing_match.lastindex and billing_match.lastindex > 1:
                address_parts = [billing_match.group(i) for i in range(1, billing_match.lastindex + 1)]
                billing_address = ", ".join([part.strip() for part in address_parts if part.strip()])
            else:
                billing_address = billing_match.group(1).strip()

            # Clean up the address
            billing_address = re.sub(r'\s+', ' ', billing_address)  # Normalize spaces
            billing_address = re.sub(r'\n+', ', ', billing_address)  # Replace newlines with commas

            # Clean the address using the new cleaning function
            billing_address = clean_address_text(billing_address)

            # Final validation - ensure it's still a meaningful address after cleaning
            if billing_address and len(billing_address) > 10:
                print(f"Found Billing Address: {billing_address} with pattern: {pattern}")
                return billing_address

    print("No Billing Address found")
    return None

def extract_product_title(text):
    """Extract Product Title from text using vertical scanning as primary method"""
    print("Looking for Product Title in text...")
    vertical_result = extract_product_title_vertical(text)
    if vertical_result:
        return vertical_result
    table_result = extract_product_title_from_table(text)
    if table_result:
        return table_result
    print("Vertical methods failed, trying horizontal patterns...")
    reliable_patterns = [
        r'([A-Za-z][^|\n]*?(?:Phone|Mobile|Laptop|Computer|Watch|Headphone|Speaker|Camera|Tablet|Book|Gift|Musical)[^|\n]*?)\s*\|',
        r'([A-Za-z][^|\n]*?(?:Samsung|Apple|Sony|LG|HP|Dell|Lenovo|Asus|Nokia|Xiaomi|OnePlus|Realme|Vivo|Oppo)[^|\n]*?)\s*\|',
        r'([A-Z][a-z]+\s+[A-Za-z][^|\n]*?)\s*\|\s*\d{4,}',
        r'"([A-Za-z][^"]{5,50})"',
        r"'([A-Za-z][^']{5,50})'",
        r'([A-Za-z][^|\n]*?[Cc]olor[^|\n]*?)\s*\|',
        r'([A-Za-z][^|\n]*?[Ss]ize[^|\n]*?)\s*\|',
        r'([A-Za-z][^|\n]*?[Mm]odel[^|\n]*?)\s*\|',
    ]

    for pattern in reliable_patterns:
        product_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if product_match:
            product = product_match.group(1).strip()
            product = clean_product_title(product)
            if is_valid_product_title(product) and len(product) > 5:
                print(f"Found Product Title (horizontal): {product} with pattern: {pattern}")
                return product

    print("No Product Title found")
    return None

def clean_product_title(product):
    """Clean and normalize product title"""
    if not product:
        return ""
    product = re.sub(r'^\d+\.\s*', '', product)  # Remove "1. " prefix
    product = re.sub(r'\[IMEI/Serial\s*No[:\s]*\]', '', product, flags=re.IGNORECASE)
    product = re.sub(r'IMEI/Serial\s*No[:\s]*', '', product, flags=re.IGNORECASE)


    product = re.sub(r'^SAC[:\s]*\d+\s*', '', product, flags=re.IGNORECASE)
    product = re.sub(r'^HSN[:\s]*\d+\s*', '', product, flags=re.IGNORECASE)
    product = re.sub(r'\s*(Packaging|Handling|Service|Processing)\s*Fee.*$', '', product, flags=re.IGNORECASE)
    product = re.sub(r'\s*(Secure|Offer|Delivery)\s*(Packaging|Handling|Fee).*$', '', product, flags=re.IGNORECASE)
    product = re.sub(r'\s*\|\s*.*$', '', product)  # Remove everything after |
    product = re.sub(r'\s*HSN.*$', '', product, re.IGNORECASE)  # Remove HSN codes
    product = re.sub(r'\s*FSN.*$', '', product, re.IGNORECASE)  # Remove FSN codes
    product = re.sub(r'\s*SKU.*$', '', product, re.IGNORECASE)  # Remove SKU codes
    product = re.sub(r'\s*₹.*$', '', product)  # Remove price
    product = re.sub(r'\s*Rs\.?.*$', '', product, re.IGNORECASE)  # Remove price
    product = re.sub(r'\s*INR.*$', '', product, re.IGNORECASE)  # Remove price
    product = re.sub(r'\s*\d+\.\d{2}.*$', '', product)  # Remove decimal prices
    product = re.sub(r'\s*\(\s*\)$', '', product)  # Remove empty parentheses
    product = re.sub(r'\s*\[\s*\]$', '', product)  # Remove empty brackets
    product = re.sub(r'\s*[A-Z0-9]{10,15}\s*$', '', product)
    product = re.sub(r'\s+', ' ', product)  # Normalize spaces
    product = re.sub(r'^[^\w]*', '', product)  # Remove leading non-word chars
    product = re.sub(r'[^\w\s\-\(\)\.]*$', '', product)  # Remove trailing special chars

    return product.strip()

def is_valid_product_title(product):
    """Validate if the extracted text is a valid product title"""
    if not product or len(product) < 3:
        return False
    if re.match(r'^\d+$', product):
        return False
    if re.match(r'^\d{1,2}[-/\.]\d{1,2}[-/\.]\d{2,4}$', product):
        return False
    if re.match(r'^[A-Z0-9\-_]+$', product) and len(product) < 10:
        return False
    if re.match(r'^E\.\s*&\s*O\.E\.$', product, re.IGNORECASE):
        return False
    reject_terms = [
        'total', 'subtotal', 'tax', 'gst', 'igst', 'sgst', 'cgst',
        'discount', 'amount', 'price', 'quantity', 'qty', 'date',
        'invoice', 'bill', 'receipt', 'order', 'shipping', 'billing',
        'address', 'phone', 'email', 'website', 'gstin', 'pan', 'cin',
        'value', 'fee', 'charge', 'handling', 'packaging', 'secure',
        'offer', 'delivery', 'service', 'processing'
    ]

    product_lower = product.lower()
    for term in reject_terms:
        if product_lower == term or product_lower.startswith(term + ' ') or product_lower.endswith(' ' + term):
            return False
    if re.search(r'(packaging|handling|service|processing|secure|offer|delivery)\s*(fee|charge)', product_lower):
        return False
    if re.search(r'\d+\s*%\s*(igst|sgst|cgst|gst|tax)', product_lower):
        return False
    if re.search(r'^(igst|sgst|cgst|gst|tax)[:\s]*\d+', product_lower):
        return False
    if not re.search(r'[A-Za-z]', product):
        return False
    if len(product) < 3 or len(product) > 200:
        return False
    return True

def similar_strings(a, b):
    """Calculate similarity between two strings using a simple approach"""
    if not a or not b:
        return 0.0
    a, b = a.lower(), b.lower()
    if a == b:
        return 1.0
    set_a, set_b = set(a), set(b)
    intersection = len(set_a & set_b)
    union = len(set_a | set_b)
    if union == 0:
        return 0.0
    return intersection / union

def extract_imei_numbers(text):
    print("Looking for IMEI/Serial numbers in text...")
    imei_numbers = []
    invoice_number_match = re.search(r"Invoice Number[:\s]+([A-Z0-9-]+)", text, re.IGNORECASE)
    invoice_number = invoice_number_match.group(1) if invoice_number_match else ""
    # Clean invoice number to remove "Tax" suffix if present
    invoice_number = re.sub(r'Tax$', '', invoice_number, flags=re.IGNORECASE).strip()
    order_id_match = re.search(r"Order ID[:\s]+([A-Z0-9-]+)", text, re.IGNORECASE)
    order_id = order_id_match.group(1) if order_id_match else ""
    print(f"Found Invoice Number: {invoice_number} and Order ID: {order_id} - will exclude these from IMEI detection")
    SIMILARITY_THRESHOLD = 0.7

    def is_too_similar_to_invoice(imei, invoice_num):
        if not invoice_num:
            return False

        similarity = similar_strings(imei, invoice_num)
        if similarity > SIMILARITY_THRESHOLD:
            print(f"Rejecting potential IMEI {imei} - too similar to invoice number {invoice_num} (similarity: {similarity:.2f})")
            return True
        return False
    imei_patterns = [
        r"IMEI/Serial No[:\s]*(.*?)(?=\n\n|\Z)",
        r"IMEI/Serial\s+No[:\s]*(.*?)(?=\n\n|\Z)",
        r"IMEI[:\s]*(.*?)(?=\n\n|\Z)",
        r"Serial No[:\s]*(.*?)(?=\n\n|\Z)"
    ]

    specific_pattern = re.search(r'\d+\.\s*\[?IMEI/Serial\s*No:?\s*([A-Z0-9][A-Z0-9\s]{8,20})', text, re.IGNORECASE)
    if specific_pattern:
        imei_with_spaces = specific_pattern.group(1)
        imei = re.sub(r'\s+', '', imei_with_spaces)
        if imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number):
            print(f"Found IMEI with specific pattern: {imei} (original: {imei_with_spaces})")
            return [imei]

    bracket_pattern = re.search(r'\]\s*([A-Z0-9][A-Z0-9\s]{8,15}?)(?=\n|$)', text)
    if bracket_pattern:
        imei_with_spaces = bracket_pattern.group(1)
        imei = re.sub(r'\s+', '', imei_with_spaces)
        if (imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number) and
            not re.match(r'^FSN[A-Z0-9]+$', imei) and
            not re.match(r'^[A-Z0-9]+FSN$', imei) and
            not re.match(r'^[A-Z]{3,}[0-9]{6,}$', imei) and
            "FSN" not in imei):
            print(f"Found IMEI with bracket pattern: {imei} (original: {imei_with_spaces})")
            if re.search(r'[A-Z][0-9]|[0-9][A-Z]', imei) and not imei.isalpha() and not imei.isdigit():
                return [imei]

    imei_section_match = re.search(r"IMEI/Serial No[:\s]*(.*?)(?=\n\n|\Z)", text, re.DOTALL | re.IGNORECASE)
    if imei_section_match:
        imei_section = imei_section_match.group(1)
        print(f"Found IMEI section: {imei_section}")
        alpha_imei_match = re.search(r'([A-Z0-9][A-Z0-9\s]{8,20})', imei_section)
        if alpha_imei_match:
            imei_with_spaces = alpha_imei_match.group(1)
            imei = re.sub(r'\s+', '', imei_with_spaces)
            if imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number):
                if (re.search(r'[A-Z][0-9]|[0-9][A-Z]', imei) and
                    not imei.isalpha() and
                    not imei.isdigit() and
                    not re.match(r'\d{2}[A-Z]{5}\d{4}[A-Z]\d?Z\w', imei) and
                    not re.match(r'^OD[0-9A-Z]+$', imei) and
                    not re.match(r'^FSN[A-Z0-9]+$', imei) and
                    not re.match(r'^[A-Z0-9]+FSN$', imei) and
                    not re.match(r'^[A-Z]{3,}[0-9]{6,}$', imei) and
                    "FSN" not in imei):
                    print(f"Found alphanumeric IMEI: {imei} (original: {imei_with_spaces})")
                    return [imei]

        imeis = []
        first_imei_match = re.search(r"(\d{11})\s+(\d{4})", imei_section)

        if first_imei_match:
            first_imei = first_imei_match.group(1) + first_imei_match.group(2)
            if first_imei != invoice_number and first_imei != order_id and not is_too_similar_to_invoice(first_imei, invoice_number):
                imeis.append(first_imei)
        additional_imeis = re.findall(r"\d{14,15}", imei_section)

        filtered_imeis = []
        for imei in additional_imeis:
            if imei != order_id and imei != invoice_number and not is_too_similar_to_invoice(imei, invoice_number):
                filtered_imeis.append(imei)
        imeis.extend(filtered_imeis)
        seen = set()
        imeis = [x for x in imeis if not (x in seen or seen.add(x))]
        if imeis:
            return imeis

    print("Looking for specific IMEI format in the entire document...")

    imei_list_match = re.search(r"IMEI/Serial No:\s*,\s*([\d\s,]+)]", text, re.IGNORECASE)
    if imei_list_match:
        imei_list_text = imei_list_match.group(1)
        individual_imeis = re.findall(r"(\d{10,15})", imei_list_text)
        filtered_imeis = []
        for imei in individual_imeis:
            if imei != order_id and imei != invoice_number and not is_too_similar_to_invoice(imei, invoice_number):
                filtered_imeis.append(imei)

        if filtered_imeis:
            print(f"Found {len(filtered_imeis)} IMEIs from list format")
            return filtered_imeis

    imei_block_match = re.search(r"\[IMEI/Serial No:\s*,(.*?)\]", text, re.DOTALL | re.IGNORECASE)
    if imei_block_match:
        imei_block = imei_block_match.group(1)
        individual_imeis = re.findall(r"(\d{10,15})", imei_block)
        filtered_imeis = []
        for imei in individual_imeis:
            if imei != order_id and imei != invoice_number and not is_too_similar_to_invoice(imei, invoice_number):
                filtered_imeis.append(imei)

        if filtered_imeis:
            print(f"Found {len(filtered_imeis)} IMEIs from block format")
            return filtered_imeis

    potential_imeis = re.findall(r'SMG\d+\s*[A-Z]+(?:\s*[A-Z0-9]+)?', text)
    if potential_imeis:
        for potential_imei in potential_imeis:
            imei = re.sub(r'\s+', '', potential_imei)
            if imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number):
                if 10 <= len(imei) <= 15:
                    print(f"Found potential SMG IMEI: {imei} (original: {potential_imei})")
                    return [imei]

    standard_imei_patterns = [
        r"IMEI[:\s]+(\d{15})",
        r"Serial Number[:\s]+(\d{15})",
        r"IMEI Number[:\s]+(\d{15})",
        r"Serial No[:\s]+(\d{15})"
    ]
    for pattern in standard_imei_patterns:
        for match in re.finditer(pattern, text):
            imei = match.group(1)
            if imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number):
                if len(imei) == 15 and imei.isdigit():
                    imei_numbers.append(imei)

    if imei_numbers:
        return imei_numbers
    print("No specific IMEI format found, searching for any potential IMEI...")
    potential_imeis = re.findall(r"[A-Z0-9]{10,15}", text)

    for imei in potential_imeis:
        if (imei != order_id and
            imei != invoice_number and
            not re.match(r"^\d{6}$", imei) and
            not re.match(r"^[A-Z]{5}\d{4}[A-Z]$", imei) and
            not re.match(r'\d{2}[A-Z]{5}\d{4}[A-Z]\d?Z\w', imei) and
            not re.match(r'^OD[0-9A-Z]+$', imei) and
            not re.match(r'^U\d+[A-Z]+\d+', imei) and
            not re.match(r'^FA[A-Z0-9]+$', imei) and
            not re.match(r'^FB[A-Z0-9]+$', imei) and
            not re.match(r'^BF[A-Z0-9]+$', imei) and
            not re.match(r'^EA[A-Z0-9]+$', imei) and
            not re.match(r'^FSN[A-Z0-9]+$', imei) and
            not re.match(r'^[A-Z0-9]+FSN$', imei) and
            not re.match(r'^[A-Z]{3,}[0-9]{6,}$', imei) and
            not re.match(r'^[0-9]{6,}[A-Z]{3,}$', imei) and
            not imei.isalpha() and
            not imei.isdigit() and
            re.search(r'[A-Z][0-9]|[0-9][A-Z]', imei) and
            "GSTIN" not in imei and
            "CIN" not in imei and
            "Invoice" not in imei and
            "FSN" not in imei):

            if invoice_number and (invoice_number in imei or imei in invoice_number):
                print(f"Skipping potential IMEI {imei} as it appears to be related to invoice number {invoice_number}")
                continue
            if is_too_similar_to_invoice(imei, invoice_number):
                continue
            if re.search(r'FSN|SKU|ASIN|PRODUCT|CODE', text[max(0, text.find(imei)-50):text.find(imei)+50], re.IGNORECASE):
                print(f"Skipping potential IMEI {imei} as it appears to be near FSN/SKU/product code context")
                continue

            print(f"Found potential IMEI in document: {imei}")
            return [imei]
    print("No IMEI numbers found in the document, leaving IMEI field empty")
    return []

def process_pdfs_from_folder(folder_path=None, api_url=None, api_key=None):
    """Process PDFs from a specified folder or default Invoice folder"""
    if folder_path is None:
        import sys
        if len(sys.argv) > 1:
            folder_path = sys.argv[1]
            print(f"📁 Using custom folder path: {folder_path}")
        else:
            folder_path = "Invoice"
            print(f"📁 Using default folder path: {folder_path}")

    print("🚀 Starting perfect PDF extraction...")
    output_file = extract_perfect_data(folder_path, api_url, api_key)

    if output_file:
        print(f"\n🎉 Perfect extraction completed!")
        print(f"📄 Output file: {output_file}")
        print(f"📋 This should now perfectly match your PDF content!")
        print(f"🔍 Check the financial data - it should be accurate now!")
        print(f"📊 All IRN codes have been extracted using comprehensive patterns!")
        print(f"📦 ZIP files have been automatically extracted and processed!")
        print(f"🌐 API integration: Data automatically sent to {api_url or DEFAULT_API_URL}")
    else:
        print(f"\n❌ Extraction failed")

    return output_file

if __name__ == "__main__":
    import sys
    folder_path = None
    api_url = DEFAULT_API_URL
    api_key = DEFAULT_API_KEY

    if len(sys.argv) > 1:
        folder_path = sys.argv[1]
    if len(sys.argv) > 2:
        api_url = sys.argv[2]
    if len(sys.argv) > 3:
        api_key = sys.argv[3]
    print("🔧 Configuration:")
    print(f"   📁 Folder: {folder_path or 'Invoice (default)'}")
    print(f"   🌐 API URL: {api_url or DEFAULT_API_URL}")
    print(f"   🔑 API Key: {'Configured' if api_key else 'Not configured'}")
    print(f"   ⏰ API Timeout: {DEFAULT_API_TIMEOUT} seconds")

    process_pdfs_from_folder(folder_path, api_url, api_key)
