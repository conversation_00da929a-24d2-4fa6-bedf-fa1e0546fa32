import fitz  
import openai
import json
import re
from typing import Dict, List, Any
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InvoiceExtractor:
    def __init__(self, openai_api_key: str):
       
        self.client = openai.OpenAI(api_key=openai_api_key)
        
    def extract_pdf_content(self, pdf_path: str) -> str:
        try:
            doc = fitz.open(pdf_path)
            full_text = ""
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                text = page.get_text()
                full_text += f"\n--- Page {page_num + 1} ---\n"
                full_text += text
                
                tables = page.find_tables()
                if tables:
                    full_text += f"\n--- Tables on Page {page_num + 1} ---\n"
                    for table_num, table in enumerate(tables):
                        full_text += f"\nTable {table_num + 1}:\n"
                        try:
                            table_data = table.extract()
                            for row in table_data:
                                full_text += " | ".join([str(cell) if cell else "" for cell in row]) + "\n"
                        except Exception as e:
                            logger.warning(f"Could not extract table {table_num + 1}: {e}")
            
            doc.close()
            return full_text
            
        except Exception as e:
            logger.error(f"Error extracting PDF content: {e}")
            raise
    
    def create_extraction_prompt(self, pdf_content: str) -> str:
        
        prompt = f"""
You are an expert at extracting structured data from invoice documents. 
Analyze the following invoice content and extract ALL relevant information into a structured JSON format.

INVOICE CONTENT:
{pdf_content}

Please extract and organize the following information into a well-structured JSON:

1. **Invoice Header Information:**
   - Invoice number
   - Invoice date
   - Due date
   - Order ID/Reference number

2. **Seller/From Address:**
   - Company name
   - Complete address
   - Contact details (phone, email if available)
   - Tax registration numbers (GST, PAN, etc.)

3. **Buyer/Bill To Address:**
   - Company/Customer name
   - Complete billing address
   - Contact details
   - Tax registration numbers if available

4. **Shipping Address:** (if different from billing)
   - Complete shipping address
   - Contact details

5. **Product/Item Details:** (Extract ALL products/items)
   For each item, include:
   - Product name/description
   - HSN/SAC code
   - Quantity
   - Unit price
   - Gross amount
   - Discount amount/percentage
   - Taxable value
   - Tax details (CGST, SGST, IGST rates and amounts)
   - Final amount

6. **Financial Summary:**
   - Subtotal
   - Total discount
   - Total taxable amount
   - Total tax amount (CGST, SGST, IGST separately)
   - Shipping and handling charges
   - Platform fee
   - Other charges/fees
   - Final total amount
   - Amount in words

7. **Additional Information:**
   - Payment terms
   - Payment method
   - Bank details
   - Notes/Terms and conditions
   - Any other relevant information

Return ONLY a valid JSON object with all extracted information. Do not include any explanatory text before or after the JSON.
Make sure to capture ALL numerical values, dates, addresses, and product details present in the invoice.
If any field is not available, use null as the value.
"""
        return prompt
    
    def extract_with_openai(self, pdf_content: str) -> Dict[str, Any]:
        try:
            prompt = self.create_extraction_prompt(pdf_content)
            
            response = self.client.chat.completions.create(
                model="gpt-4",  
                messages=[
                    {
                        "role": "system", 
                        "content": "You are an expert data extraction assistant. You extract information from invoices and return only valid JSON."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=0.1,
                max_tokens=4000
            )
            
            response_content = response.choices[0].message.content.strip()
            
            try:
                extracted_data = json.loads(response_content)
                return extracted_data
            except json.JSONDecodeError:
                
                json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
                if json_match:
                    extracted_data = json.loads(json_match.group())
                    return extracted_data
                else:
                    raise ValueError("Could not extract valid JSON from OpenAI response")
                    
        except Exception as e:
            logger.error(f"Error with OpenAI extraction: {e}")
            raise
    
    def process_invoice(self, pdf_path: str, output_file: str = None) -> Dict[str, Any]:
        try:
            logger.info(f"Processing PDF: {pdf_path}")
            
            logger.info("Extracting PDF content...")
            pdf_content = self.extract_pdf_content(pdf_path)
            
            logger.info("Processing with OpenAI...")
            structured_data = self.extract_with_openai(pdf_content)
            
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(structured_data, f, indent=2, ensure_ascii=False)
                logger.info(f"Results saved to: {output_file}")
            
            logger.info("Processing completed successfully!")
            return structured_data
            
        except Exception as e:
            logger.error(f"Error processing invoice: {e}")
            raise

def main():

    PDF_PATH = r"C:\Users\<USER>\Desktop\All Scraping\Invoice\OD330725585114150100.pdf" 
    OPENAI_API_KEY = "openai-api-key"  # Replace with your API key
    OUTPUT_FILE = "extracted_invoice_data.json"  

    try:
        extractor = InvoiceExtractor(OPENAI_API_KEY)
        
        result = extractor.process_invoice(PDF_PATH, OUTPUT_FILE)
        
        print("Extracted Invoice Data:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()