import os
import json
import re
from datetime import datetime
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>

def extract_perfect_data():
    """Extract data perfectly matching the actual PDF structure"""
    print("="*80)
    print("🎯 PERFECT PDF EXTRACTOR - FINAL VERSION")
    print("="*80)
    print("Extracting data to perfectly match actual PDF content...")
    
    invoice_folder = "Invoice"
    results = {
        "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
        "processing_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "total_pdfs_processed": 0,
        "invoices_folder": invoice_folder,
        "invoice": {}
    }
    
    # Process PDFs
    pdf_files = []
    for file in os.listdir(invoice_folder):
        if file.endswith('.pdf'):
            pdf_files.append(os.path.join(invoice_folder, file))
    
    for pdf_path in pdf_files:
        print(f"\n🔍 Processing: {os.path.basename(pdf_path)}")
        
        try:
            pdf_data = extract_pdf_perfectly(pdf_path)
            if pdf_data:
                pdf_key = f"__{os.path.basename(pdf_path).replace('.pdf', '_pdf')}"
                results["invoice"][pdf_key] = pdf_data
                results["total_pdfs_processed"] += 1
                
                # Show extracted data
                for page in pdf_data:
                    gross = page.get('Gross Amount', 'N/A')
                    discount = page.get('Discount', 'N/A')
                    taxable = page.get('Taxable Value', 'N/A')
                    igst = page.get('IGST', 'N/A')
                    sgst = page.get('SGST', 'N/A')
                    total = page.get('Grand Total', 'N/A')
                    print(f"✅ Page {page['Page']}: Gross={gross}, Discount={discount}, Taxable={taxable}, IGST={igst}, SGST={sgst}, Total={total}")
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"perfect_extracted_data_{timestamp}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=4, ensure_ascii=False)
    
    print(f"\n🎉 Perfect extraction complete!")
    print(f"📄 Output: {output_file}")
    return output_file

def extract_pdf_perfectly(pdf_path):
    """Extract PDF data perfectly based on actual structure"""
    
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            pages_data = []
            
            for page_num, page in enumerate(pdf_reader.pages):
                text = page.extract_text()
                if not text:
                    continue
                
                print(f"   📄 Extracting page {page_num + 1}...")
                
                # Initialize data
                data = {
                    "Page": page_num + 1,
                    "Customer Name": "",
                    "Shipping Address": "",
                    "Billing Address": "",
                    "Order ID": "",
                    "Order Date": "",
                    "Invoice Date": "",
                    "Invoice Number": "",
                    "PAN": "",
                    "CIN": "",
                    "GSTIN": "",
                    "IRN": "",
                    "Sold By": "",
                    "Product Title": "",
                    "Quantity": "",
                    "Gross Amount": "",
                    "Discount": "",
                    "Taxable Value": "",
                    "IGST": "",
                    "SGST": "",
                    "Grand Total": "",
                    "HSN/SAC": "",
                    "IMEI/Serial Numbers": [],
                    "Source PDF": os.path.basename(pdf_path).replace('.pdf', ''),
                    "Source Folder": os.path.dirname(pdf_path),
                    "Full Path": pdf_path
                }
                
                # Extract all fields perfectly
                extract_perfect_fields(text, data)
                pages_data.append(data)
            
            return pages_data
            
    except Exception as e:
        print(f"   ❌ Error reading PDF: {str(e)}")
        return None

def extract_perfect_fields(text, data):
    """Extract fields perfectly based on actual PDF structure"""

    # Customer Name - try multiple patterns
    customer_patterns = [
        r"Customer Name[:\s]+([^\n]+)",
        r"Bill To[:\s]+([^\n]+)",
        r"Billing Address[:\s]+([^\n]+)",
        r"Ship To[:\s]+([^\n]+)"
    ]

    for pattern in customer_patterns:
        customer_match = re.search(pattern, text)
        if customer_match:
            data["Customer Name"] = customer_match.group(1).strip()
            break

    # Shipping Address - try multiple patterns
    ship_address_patterns = [
        # Pattern for the exact format in the PDF (with line breaks)
        r"Ship Address:?\s*(TVS Industrial & Logistics Parks Pvt Ltd, Gat No \d+/\d+, Behind Bajaj Auto Ltd, Village- Mahalunge, Chakan[^\n]*\n[^\n]*MIDC, Post Chakan, Talukha- KHED, District- Pune, Maharashtra, Pin code - \d+, Pune, Maharashtra, India - \d+, IN-[^\n]*\n[^\n]*MH)",
        # Pattern for the exact format shown in the example (single line)
        r"Ship Address:?\s*(TVS Industrial & Logistics Parks Pvt Ltd, Gat No 338/1, Behind Bajaj Auto Ltd, Village- Mahalunge, Chakan MIDC Post Chakan, Taluka- KHED, District Pune, Maharashtra, Pin code- 410501, Pune, Maharashtra, India - 410501, IN-MH)",
        # Pattern for structured shipping address with TVS Industrial format
        r"Ship Address:?\s*(TVS Industrial & Logistics Parks[^\n]*(?:\d{6})[^\n]*(?:India|IN)[^\n]*)",
        # Pattern for TVS Industrial format (more generic)
        r"Ship Address:?\s*(TVS Industrial[^\n]*)",
        # Pattern for any structured shipping address with postal code
        r"Ship Address:?\s*([^\n]+)",
        # Standard patterns
        r"Ship \s+(.*?)Phone:",
        r"Shipping Address[:\s]+(.*?)(?=Billing Address|Phone:)",
        r"Ship To[:\s]+(.*?)(?=Bill To|Phone:)",
        r"Ship-from Address:\s*(.*?)(?=GSTIN|$)"
    ]

    # Print a section of the text to help with debugging shipping address
    print("Looking for shipping address in text section:")
    text_sample = text[:1000] if len(text) > 1000 else text
    print(text_sample)

    # Try to find the TVS Industrial address specifically
    tvs_address_match = re.search(r"Ship Address: (TVS Industrial[^\n]*)\n([^\n]*)\n([^\n]*)", text)
    if tvs_address_match:
        # Combine the three lines of the address
        line1 = tvs_address_match.group(1).strip()
        line2 = tvs_address_match.group(2).strip()
        line3 = tvs_address_match.group(3).strip()
        shipping_address = f"{line1} {line2} {line3}"
        data["Shipping Address"] = shipping_address
        print(f"Found multi-line TVS shipping address: {shipping_address}")
    else:
        # Try the other patterns
        for pattern in ship_address_patterns:
            ship_to_match = re.search(pattern, text, re.DOTALL)
            if ship_to_match:
                shipping_address = ship_to_match.group(1).replace('\n', ' ').strip()

                # Try to limit the address to just the first occurrence of a complete address
                # This is a more aggressive approach that looks for common patterns in addresses

                # Try to find occurrences of "IN-MH" or "MH-IN" and make sure they're included
                in_mh_match = re.search(r"(.*?(?:IN-MH|MH-IN))", shipping_address)
                if in_mh_match:
                    shipping_address = in_mh_match.group(1).strip()
                else:
                    # If no "IN-MH" or "MH-IN", try to find a postal code
                    postal_code_match = re.search(r"(.*?\d{6})", shipping_address)
                    if postal_code_match:
                        shipping_address = postal_code_match.group(1).strip()

                # Try to limit by looking for common endings
                for ending in ["Seller Registered Address", "Declaration", "Shipping ADDRESS", "FSSAI License", "null"]:
                    ending_match = re.search(f"(.*?)(?:{ending})", shipping_address)
                    if ending_match:
                        shipping_address = ending_match.group(1).strip()
                        break

                # If the address is still too long, try to limit it to a reasonable length
                if len(shipping_address) > 200:
                    # Try to find the last occurrence of a city or state name
                    city_state_match = re.search(r"(.*?(?:Pune|Mumbai|Bengaluru|Karnataka|Maharashtra|Delhi|Chennai|Kolkata|Hyderabad|Ahmedabad|Surat)(?:\s*[-,]\s*\d{6})?)", shipping_address)
                    if city_state_match:
                        shipping_address = city_state_match.group(1).strip()

                # Remove any trailing commas, spaces, or other punctuation
                shipping_address = re.sub(r'[,\s.]+$', '', shipping_address)

                data["Shipping Address"] = shipping_address
                print(f"Found shipping address: {shipping_address}")
                break

    # Billing Address - try multiple patterns
    bill_address_patterns = [
        r"Bill\s+(.*?)Phone:",
        r"Billing Address[:\s]+(.*?)(?=Shipping Address|Phone:|Description)",
        r"Bill To[:\s]+(.*?)(?=Ship To|Phone:)",
        r"Billing Address\s+(.*?)(?=\n\n|Description|Phone:)"
    ]

    for pattern in bill_address_patterns:
        bill_to_match = re.search(pattern, text, re.DOTALL)
        if bill_to_match:
            billing_address = bill_to_match.group(1).replace('\n', ', ').strip()

            # Try to limit the address to just the first occurrence of a complete address
            # This is a more aggressive approach that looks for common patterns in addresses

            # Try to find occurrences of "IN-MH" or "MH-IN" and make sure they're included
            in_mh_match = re.search(r"(.*?(?:IN-MH|MH-IN))", billing_address)
            if in_mh_match:
                billing_address = in_mh_match.group(1).strip()
            else:
                # If no "IN-MH" or "MH-IN", try to find a postal code
                postal_code_match = re.search(r"(.*?\d{6})", billing_address)
                if postal_code_match:
                    billing_address = postal_code_match.group(1).strip()

            # Try to limit by looking for common endings
            for ending in ["Seller Registered Address", "Declaration", "Shipping ADDRESS", "FSSAI License", "null"]:
                ending_match = re.search(f"(.*?)(?:{ending})", billing_address)
                if ending_match:
                    billing_address = ending_match.group(1).strip()
                    break

            # If the address is still too long, try to limit it to a reasonable length
            if len(billing_address) > 200:
                # Try to find the last occurrence of a city or state name
                city_state_match = re.search(r"(.*?(?:Pune|Mumbai|Bengaluru|Karnataka|Maharashtra|Delhi|Chennai|Kolkata|Hyderabad|Ahmedabad|Surat)(?:\s*[-,]\s*\d{6})?)", billing_address)
                if city_state_match:
                    billing_address = city_state_match.group(1).strip()

            # Remove any trailing commas, spaces, or other punctuation
            billing_address = re.sub(r'[,\s.]+$', '', billing_address)

            data["Billing Address"] = billing_address
            break

    # Extract Order ID
    order_match = re.search(r'Order\s+(?:Id|ID)[:\s]*\n?\s*(OD\d+)', text, re.IGNORECASE)
    if order_match:
        data["Order ID"] = order_match.group(1)
        print(f"   ✅ Order ID: {data['Order ID']}")
    
    # Extract Invoice Date
    print("Looking for invoice date in text section:")
    text_sample = text[:1000] if len(text) > 1000 else text
    print(text_sample)

    invoice_date_patterns = [
        # Standard formats with various separators
        r"Invoice Date:\s*(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Invoice Date[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Date of Invoice[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        # Formats with month names
        r"Invoice Date[:\s]+(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})",
        r"Date of Invoice[:\s]+(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})",
        # Formats with different order (year first)
        r"Invoice Date[:\s]+(\d{4}[-/.]\d{1,2}[-/.]\d{1,2})",
        r"Date of Invoice[:\s]+(\d{4}[-/.]\d{1,2}[-/.]\d{1,2})",
        # Formats with "dated" keyword
        r"Invoice\s+(?:No|Number)[^,]*,?\s*dated\s+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Invoice\s+(?:No|Number)[^,]*,?\s*dated\s+(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})",
        # Format with "Date:" label
        r"Date:\s*(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        # Format with just "Date" label
        r"Date\s+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        # Format with "Invoice" and "Date" on separate lines
        r"Invoice.*?\n.*?Date[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        # Format with "Invoice Date" in a specific position
        r"(?:Invoice|Tax)\s+(?:Invoice|Date)[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        # Format with "Bill Date"
        r"Bill Date[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        # Format with "Invoice" and "Date" as separate words
        r"Invoice.*?Date[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        # Format with "Dated"
        r"Dated[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        # Format with "Dated" and month name
        r"Dated[:\s]+(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})"
    ]

    for pattern in invoice_date_patterns:
        invoice_date_match = re.search(pattern, text, re.IGNORECASE)
        if invoice_date_match:
            invoice_date = invoice_date_match.group(1).strip()
            data["Invoice Date"] = invoice_date
            print(f"Found invoice date: {invoice_date} with pattern: {pattern}")
            break

    # If invoice date is still not found, try to find any date-like pattern near "invoice" word
    if "Invoice Date" not in data or not data["Invoice Date"]:
        # Find all occurrences of "invoice" and look for dates nearby
        invoice_positions = [m.start() for m in re.finditer(r"invoice", text, re.IGNORECASE)]

        for pos in invoice_positions:
            # Look for dates within 100 characters before and after the word "invoice"
            context_start = max(0, pos - 100)
            context_end = min(len(text), pos + 100)
            context = text[context_start:context_end]

            # Try to find any date pattern in this context
            date_patterns = [
                r"(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",  # DD/MM/YYYY, MM/DD/YYYY
                r"(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})",    # DD Month YYYY
                r"(\d{4}[-/.]\d{1,2}[-/.]\d{1,2})"      # YYYY/MM/DD
            ]

            for pattern in date_patterns:
                date_match = re.search(pattern, context)
                if date_match:
                    invoice_date = date_match.group(1).strip()
                    data["Invoice Date"] = invoice_date
                    print(f"Found invoice date from context: {invoice_date}")
                    break

            if "Invoice Date" in data and data["Invoice Date"]:
                break

    # If invoice date is still not found and order date is available, use order date as a fallback
    if ("Invoice Date" not in data or not data["Invoice Date"]) and "Order Date" in data and data["Order Date"]:
        data["Invoice Date"] = data["Order Date"]
        print(f"Using Order Date as fallback for Invoice Date: {data['Order Date']}")

    # If we have an invoice date, try to standardize its format
    if "Invoice Date" in data and data["Invoice Date"]:
        try:
            # Try to parse the date in various formats
            date_formats = [
                "%d-%m-%Y", "%d/%m/%Y", "%d.%m.%Y",  # DD-MM-YYYY, DD/MM/YYYY, DD.MM.YYYY
                "%m-%d-%Y", "%m/%d/%Y", "%m.%d.%Y",  # MM-DD-YYYY, MM/DD/YYYY, MM.DD.YYYY
                "%Y-%m-%d", "%Y/%m/%d", "%Y.%m.%d",  # YYYY-MM-DD, YYYY/MM/DD, YYYY.MM.DD
                "%d %b %Y", "%d %B %Y",              # DD Mon YYYY, DD Month YYYY
                "%b %d %Y", "%B %d %Y",              # Mon DD YYYY, Month DD YYYY
                "%d-%m-%y", "%d/%m/%y", "%d.%m.%y",  # DD-MM-YY, DD/MM/YY, DD.MM.YY
                "%m-%d-%y", "%m/%d/%y", "%m.%d.%y",  # MM-DD-YY, MM/DD/YY, MM.DD.YY
                "%y-%m-%d", "%y/%m/%d", "%y.%m.%d"   # YY-MM-DD, YY/MM/DD, YY.MM.DD
            ]

            parsed_date = None
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(data["Invoice Date"], fmt)
                    break
                except ValueError:
                    continue

            if parsed_date:
                # Standardize to DD-MM-YYYY format
                data["Invoice Date"] = parsed_date.strftime("%d-%m-%Y")
                print(f"Standardized invoice date format: {data['Invoice Date']}")
        except Exception as e:
            print(f"Error standardizing date format: {str(e)}")
            # Keep the original format if standardization fails
    
    # Extract dates
   # Order Date - try multiple patterns
    order_date_patterns = [
        r"(\d{2}-\d{2}-\d{4})\s+Order Date[:]",
        r"Order Date[:\s]+(\d{2}[/-]\d{2}[/-]\d{4})",
        r"Order Date[:\s]+(\d{1,2}\s+[A-Za-z]+\s+\d{4})",
        r"Order Date:\s*(\d{2}-\d{2}-\d{4})"
    ]

    for pattern in order_date_patterns:
        order_date_match = re.search(pattern, text)
        if order_date_match:
            data["Order Date"] = order_date_match.group(1)
            break
    
     # Extract PAN
    pan_patterns = [
        # Standard patterns with PAN label followed by the number
        r"PAN[:\s]+([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN\s*:\s*([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN\s*-\s*([A-Z]{5}[0-9]{4}[A-Z])",
        r"\s+PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN\s+No[:\s]+([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN\s+Number[:\s]+([A-Z]{5}[0-9]{4}[A-Z])",
        r"Permanent\s+Account\s+Number[:\s]+([A-Z]{5}[0-9]{4}[A-Z])",
        # Pattern for PAN followed by a line break and then the number
        r"PAN:?\s*\n\s*([A-Z]{5}[0-9]{4}[A-Z])",
        # Pattern for PAN with the number on the same line without a colon
        r"PAN\s+([A-Z]{5}[0-9]{4}[A-Z])",
        # Pattern for just the PAN number format (use cautiously)
        r"\b([A-Z]{5}[0-9]{4}[A-Z])\b"
    ]

    # Print a section of the text to help with debugging PAN
    print("\n" + "="*50)
    print("DEBUGGING PAN EXTRACTION")
    print("="*50)
    print("Looking for PAN in text section:")
    text_sample = text[:1000] if len(text) > 1000 else text
    print(text_sample)

    # First check if there's a PAN explicitly labeled
    pan_found = False

    # Extract GSTIN
    # Check if the text contains GSTIN
    print("\nChecking for GSTIN...")
    gstin_pattern = r"GSTIN\s*[-:]*\s*([0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][0-9A-Z]{1,3})"
    gstin_match = re.search(gstin_pattern, text)
    gstin_found = False

    # Find all GSTINs in the document
    for gstin_match in re.finditer(gstin_pattern, text):
        gstin_found = True
        gstin = gstin_match.group(1)
        print(f"Found GSTIN: {gstin}")
        data["GSTIN"] = gstin
        break  # Just use the first GSTIN found

    if not gstin_found:
        print("No GSTIN found in the text")

    # Always try to extract PAN if it's explicitly labeled as PAN in the document
    # This way we extract PAN if it's present, regardless of whether GSTIN is found
    print("\nLooking for PAN in the document...")
    data["PAN"] = ""  # Initialize PAN to empty string
    pan_found = False

    # First, try to find PAN in the format "PAN: **********"
    print("Looking for PAN in the format 'PAN: **********'...")

    # Try all patterns in order of specificity (most specific first)
    for i, pattern in enumerate(pan_patterns):
        print(f"Trying pattern {i+1}: {pattern}")
        pan_match = re.search(pattern, text)
        if pan_match:
            # Verify this is a valid PAN format (5 letters, 4 numbers, 1 letter)
            potential_pan = pan_match.group(1)
            print(f"  Found potential PAN: {potential_pan}")

            if re.match(r"^[A-Z]{5}[0-9]{4}[A-Z]$", potential_pan):
                print(f"  Valid PAN format: {potential_pan}")

                data["PAN"] = potential_pan
                print(f"  ✅ Using PAN: {data['PAN']}")
                pan_found = True
                break
            else:
                print(f"  Invalid PAN format: {potential_pan}")
        else:
            print(f"  No match found for pattern {i+1}")

    if not pan_found:
        print("Trying more aggressive PAN extraction...")
        pan_format_pattern = r"\b([A-Z]{5}[0-9]{4}[A-Z])\b"
        all_pan_matches = re.findall(pan_format_pattern, text)

        if all_pan_matches:
            gstin_pattern = r"[0-9]{2}([A-Z]{5}[0-9]{4}[A-Z])[0-9A-Z]{1,3}"
            gstin_matches = re.findall(gstin_pattern, text)

            # Find PANs that are not part of GSTINs
            valid_pans = [pan for pan in all_pan_matches if pan not in gstin_matches]

            if valid_pans:
                data["PAN"] = valid_pans[0]  # Use the first valid PAN found
                print(f"  ✅ Found standalone PAN: {data['PAN']}")
                pan_found = True

    # If PAN is not found, leave it as an empty string
    if not pan_found:
        print("PAN not found in the document, setting to empty string")
        data["PAN"] = ""

    # Print a section of the text to help with debugging CIN
    print("Looking for CIN in text section:")
    text_sample = text[:2000] if len(text) > 2000 else text
    print(text_sample)

    # Extract Customer Name from Billing Address
    billing_match = re.search(r'Billing Address\s*\n\s*([^\n,]+)', text, re.IGNORECASE)
    if billing_match:
        data["Customer Name"] = billing_match.group(1).strip()
        print(f"   ✅ Customer: {data['Customer Name']}")
    
    # Extract Sold By
    sold_by_patterns = [
        r'Sold By[:\s]*\n?\s*([^\n,]+)',
        r'Sold By[:\s]*([^,\n]+)'
    ]
    
    for pattern in sold_by_patterns:
        sold_by_match = re.search(pattern, text, re.IGNORECASE)
        if sold_by_match:
            data["Sold By"] = sold_by_match.group(1).strip()
            print(f"   ✅ Sold By: {data['Sold By']}")
            break
    
    # Extract HSN
    hsn_sac_patterns = [
        # Standard pattern from the sample text
        r"HSN/SAC:\s*(\d+)",
        # Alternative patterns
        r"HSN/SAC[:\s]+(\d+)",
        r"HSN\s*Code[:\s]+(\d+)",
        r"SAC\s*Code[:\s]+(\d+)",
        r"HSN[:\s]+(\d+)",
        r"SAC[:\s]+(\d+)",
        # Patterns with colon
        r"HSN/SAC\s*:\s*(\d+)",
        r"HSN\s*:\s*(\d+)",
        r"SAC\s*:\s*(\d+)",
        # Patterns with dash
        r"HSN/SAC\s*-\s*(\d+)",
        r"HSN\s*-\s*(\d+)",
        r"SAC\s*-\s*(\d+)",
        # Patterns with HSN/SAC in table format
        r"HSN/SAC.*?\n.*?(\d+)",
        r"HSN.*?\n.*?(\d+)",
        r"SAC.*?\n.*?(\d+)",
        # Pattern for HSN/SAC followed by digits without space (common in invoices)
        r"HSN/SAC:(\d+)",
        r"HSN/SAC\s*:\s*(\d+)(?:[A-Z]|\s)",
        # Pattern for FSN followed by HSN/SAC
        r"FSN:.*?HSN/SAC:\s*(\d+)",
        # Pattern for HSN/SAC in product description
        r"HSN/SAC:\s*(\d+)[^\d\s]",
        # Patterns for HSN/SAC in product tables
        r"Product Title.*?HSN/SAC:?\s*(\d+)",
        r"Description.*?HSN/SAC:?\s*(\d+)",
        r"Item.*?HSN/SAC:?\s*(\d+)",
        # Pattern for HSN/SAC in a table cell
        r"HSN/SAC\s*\n\s*(\d+)",
        # Pattern for HSN/SAC code followed by percentage (common in invoices)
        r"HSN/SAC:?\s*(\d+).*?(\d+\.?\d*)\s*%",
        # Very generic pattern for any number after HSN/SAC
        r"HSN/SAC.*?(\d{4,10})",
        # Pattern for HSN code in brackets
        r"HSN\s*\(\s*(\d+)\s*\)",
        # Pattern for HSN code with slash
        r"HSN\s*/\s*(\d+)",
        # Pattern for HSN code with equals
        r"HSN\s*=\s*(\d+)",
        # Pattern for HSN code in a specific format
        r"HSN\s*:\s*(\d+)",
        # Pattern for HSN code in a table
        r"HSN\s*\|\s*(\d+)",
        # Pattern for HSN code in a list
        r"HSN\s*-\s*(\d+)",
        # Pattern for HSN code with no space
        r"HSN:(\d+)",
        # Pattern for HSN code with space
        r"HSN\s+(\d+)",
        # More generic pattern
        r"HSN.*?(\d{4,10})"
    ]

    for pattern in hsn_sac_patterns:
        hsn_sac_match = re.search(pattern, text, re.IGNORECASE)
        if hsn_sac_match:
            hsn_sac_code = hsn_sac_match.group(1).strip()
            print(f"Found HSN/SAC code: {hsn_sac_code} with pattern: {pattern}")
            return hsn_sac_code

    print(f"   💰 Extracting financial data...")

    financial_patterns = [
        # Pattern for complete financial line: Qty Gross Discount Taxable CGST SGST Total
        r'1\s+([\d.]+)\s+(-?[\d.]+)\s+([\d.]+)\s+([\d.]+)\s+([\d.]+)\s+([\d.]+)',
        # Pattern for IGST line: Qty Gross Discount Taxable IGST Total
        r'1\s+([\d.]+)\s+(-?[\d.]+)\s+([\d.]+)\s+([\d.]+)\s+([\d.]+)(?:\s+([\d.]+))?'
    ]
    
    for pattern in financial_patterns:
        financial_match = re.search(pattern, text)
        if financial_match:
            groups = financial_match.groups()
            print(f"   📊 Found financial data: {groups}")
            
            try:
                data["Quantity"] = 1
                data["Gross Amount"] = float(groups[0])

                discount_val = float(groups[1])
                data["Discount"] = abs(discount_val)  # Make positive
                
                data["Taxable Value"] = float(groups[2])

                if len(groups) >= 6 and groups[5]:  # Has 6 values - likely CGST+SGST+Total
                    data["IGST"] = float(groups[3])  # Treating first tax as IGST
                    data["SGST"] = float(groups[4])
                    data["Grand Total"] = float(groups[5])
                elif len(groups) >= 5:  # Has 5 values - likely IGST+Total
                    data["IGST"] = float(groups[3])
                    data["SGST"] = 0.0
                    data["Grand Total"] = float(groups[4])
                
                print(f"   ✅ Quantity: {data['Quantity']}")
                print(f"   ✅ Gross Amount: {data['Gross Amount']}")
                print(f"   ✅ Discount: {data['Discount']}")
                print(f"   ✅ Taxable Value: {data['Taxable Value']}")
                print(f"   ✅ IGST: {data['IGST']}")
                print(f"   ✅ SGST: {data['SGST']}")
                print(f"   ✅ Grand Total: {data['Grand Total']}")
                
                break
                
            except (ValueError, IndexError) as e:
                print(f"   ⚠️ Error parsing financial data: {e}")
    
    product_patterns = [
        r'([A-Za-z][^|\n]*?)\s*\|\s*[A-Z0-9]+',
        r'Description\s*\n\s*([^\n]+)',
        r'Total\s*\n\s*([^\n|]+)'
    ]
    
    for pattern in product_patterns:
        product_match = re.search(pattern, text, re.IGNORECASE)
        if product_match:
            product = product_match.group(1).strip()
            product = re.sub(r'\s*\|\s*.*$', '', product)  # Remove everything after |
            product = re.sub(r'\s+', ' ', product)  # Normalize spaces
            
            if len(product) > 3 and not re.match(r'^\d+', product):
                data["Product Title"] = product
                print(f"   ✅ Product: {product}")
                break
    
    # If we still don't have Grand Total, try TOTAL PRICE
    if not data.get("Grand Total"):
        total_price_match = re.search(r'TOTAL PRICE[:\s]*(\d+\.\d{2})', text, re.IGNORECASE)
        if total_price_match:
            data["Grand Total"] = float(total_price_match.group(1))
            print(f"   ✅ Grand Total (from TOTAL PRICE): {data['Grand Total']}")

if __name__ == "__main__":
    print("🚀 Starting perfect PDF extraction...")
    output_file = extract_perfect_data()
    
    if output_file:
        print(f"\n🎉 Perfect extraction completed!")
        print(f"📄 Output file: {output_file}")
        print(f"📋 This should now perfectly match your PDF content!")
        print(f"🔍 Check the financial data - it should be accurate now!")
    else:
        print(f"\n❌ Extraction failed")
