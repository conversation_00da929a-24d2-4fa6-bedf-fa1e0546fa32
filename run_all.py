import subprocess
import os

# Define script paths
base_dir = os.getcwd()
script1 = os.path.join(base_dir, "downloads.py")
script2 = os.path.join(base_dir, "last_final.py")

def run_script(script_path):
    print(f"▶️ Running: {script_path}")
    result = subprocess.run(["python", script_path], check=True)
    print(f"✅ Finished: {script_path}\n")

if __name__ == "__main__":
    try:
        run_script(script1)  # Step 1: Run Flipkart scraping and invoice downloading
        run_script(script2)  # Step 2: Run PDF parsing on downloaded invoices
        print("🎉 All tasks completed successfully.")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running script: {e}")
