from google.cloud import documentai_v1 as documentai
import os
import json

# Path to your service account key
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "path/to/your-service-account.json"

# Configuration
project_id = "your-project-id"
location = "us"  # or "eu"
processor_id = "your-processor-id"  # Use the Invoice Parser processor

def process_invoice(file_path):
    # Document AI client
    client = documentai.DocumentUnderstandingServiceClient()

    # Load PDF
    with open(file_path, "rb") as pdf_file:
        pdf_content = pdf_file.read()

    # Set request
    name = f"projects/{project_id}/locations/{location}/processors/{processor_id}"

    document = {
        "content": pdf_content,
        "mime_type": "application/pdf"
    }

    request = {
        "name": name,
        "raw_document": document
    }

    # Process the document
    result = client.process_document(request=request)
    document_object = result.document

    # Convert to JSON
    doc_json = document_object.to_dict()

    # Save result
    output_file = os.path.splitext(file_path)[0] + "_parsed.json"
    with open(output_file, "w") as f:
        json.dump(doc_json, f, indent=4)

    print(f"✅ Parsed JSON saved to {output_file}")

# Example usage
process_invoice("invoice.pdf")
